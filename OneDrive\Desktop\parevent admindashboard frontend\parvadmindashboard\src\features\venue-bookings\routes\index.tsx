import React from 'react';
import { Routes, Route } from 'react-router-dom';
import VenueBookingsList from '../components/VenueBookingsList';

const VenueBookingsRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<VenueBookingsList />} />
      <Route path="upcoming" element={<div>Upcoming Bookings - Coming Soon</div>} />
      <Route path="edit/:id" element={<div>Edit Booking - Coming Soon</div>} />
      <Route path=":id" element={<div>Booking Details - Coming Soon</div>} />
    </Routes>
  );
};

export default VenueBookingsRoutes;
