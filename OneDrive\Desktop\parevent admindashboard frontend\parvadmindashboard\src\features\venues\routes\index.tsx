import React from 'react';
import { Routes, Route } from 'react-router-dom';
import VenuesList from '../components/VenuesList';
import VenueForm from '../components/VenueForm';
import VenueDetail from '../components/VenueDetail';

const VenuesRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<VenuesList />} />
      <Route path="create" element={<VenueForm mode="create" />} />
      <Route path="edit/:id" element={<VenueForm mode="edit" />} />
      <Route path=":id" element={<VenueDetail />} />
    </Routes>
  );
};

export default VenuesRoutes;
