import React from 'react';
import { Routes, Route } from 'react-router-dom';
import VenuesList from '../components/VenuesList';
import VenueForm from '../components/VenueForm';

const VenuesRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<VenuesList />} />
      <Route path="create" element={<VenueForm mode="create" />} />
      <Route path="edit/:id" element={<VenueForm mode="edit" />} />
      <Route path=":id" element={<div>Venue Details - Coming Soon</div>} />
    </Routes>
  );
};

export default VenuesRoutes;
