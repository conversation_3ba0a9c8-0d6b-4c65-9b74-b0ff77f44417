import axios from 'axios';
import { venuesEndpoints } from '@/globalurl/baseurl';
import { CreateVenueRequest, UpdateVenueRequest, VenueFilters } from '../types/venue';
import { mockVenues } from '@/utils/mockData';

export const venuesApi = {
  // Get all venues with optional filters
  getAll: async (filters?: VenueFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.venueType) params.append('venueType', filters.venueType);
      if (filters?.city) params.append('city', filters.city);
      if (filters?.minPrice) params.append('minPrice', filters.minPrice.toString());
      if (filters?.maxPrice) params.append('maxPrice', filters.maxPrice.toString());
      if (filters?.minCapacity) params.append('minCapacity', filters.minCapacity.toString());
      if (filters?.amenities?.length) params.append('amenities', filters.amenities.join(','));
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);

      const url = params.toString() ? `${venuesEndpoints.getAll}?${params}` : venuesEndpoints.getAll;
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.warn('Venues API not available, using mock data:', error);
      return {
        success: true,
        data: mockVenues,
        message: 'Mock data loaded'
      };
    }
  },

  // Get venue by ID or fixedId
  getById: async (id: string) => {
    const response = await axios.get(venuesEndpoints.getById(id));
    return response.data;
  },

  // Create new venue
  create: async (data: CreateVenueRequest) => {
    try {
      const formData = new FormData();

      formData.append('name', data.name);
      formData.append('venueType', data.venueType);
      formData.append('location', data.location);
      formData.append('capacity', data.capacity.toString());
      formData.append('seats', data.seats.toString());
      if (data.sortOrder !== undefined) formData.append('sortOrder', data.sortOrder.toString());
      formData.append('image', data.image);

      const response = await axios.post(venuesEndpoints.create, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Venue create error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to create venue',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Update venue
  update: async (id: string, data: UpdateVenueRequest) => {
    const formData = new FormData();
    if (data.name) formData.append('name', data.name);
    if (data.venueType) formData.append('venueType', data.venueType);
    if (data.location) formData.append('location', data.location);
    if (data.capacity !== undefined) formData.append('capacity', data.capacity.toString());
    if (data.seats !== undefined) formData.append('seats', data.seats.toString());
    if (data.sortOrder !== undefined) formData.append('sortOrder', data.sortOrder.toString());
    if (data.image) formData.append('image', data.image);

    const response = await axios.put(venuesEndpoints.update(id), formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Delete venue
  delete: async (id: string) => {
    const response = await axios.delete(venuesEndpoints.delete(id));
    return response.data;
  },
};
