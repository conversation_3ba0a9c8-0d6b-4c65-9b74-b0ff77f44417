import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ArrowLeft, Edit, Trash2, Calendar, Star, User, Heart } from 'lucide-react';
import { Review, RELATIONSHIP_TYPES, EVENT_TYPES } from '../types/review';
import { reviewsApi } from '../api/reviewsApi';

const ReviewDetail: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [review, setReview] = useState<Review | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchReview();
    }
  }, [id]);

  const fetchReview = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await reviewsApi.getById(id);
      if (response.success) {
        setReview(response.data);
      } else {
        toast.error('Review not found');
        navigate('/reviews');
      }
    } catch (error) {
      console.error('Error fetching review:', error);
      toast.error('Failed to fetch review data');
      navigate('/reviews');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!review || !window.confirm('Are you sure you want to delete this review?')) {
      return;
    }

    try {
      const response = await reviewsApi.delete(review._id);
      if (response.success) {
        toast.success('Review deleted successfully');
        navigate('/reviews');
      } else {
        toast.error('Failed to delete review');
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Failed to delete review');
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={20}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!review) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Review Not Found</h1>
          <Link
            to="/reviews"
            className="text-blue-500 hover:text-blue-600 inline-flex items-center gap-2"
          >
            <ArrowLeft size={20} />
            Back to Reviews
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link
            to="/reviews"
            className="text-gray-500 hover:text-gray-700 flex items-center gap-2"
          >
            <ArrowLeft size={20} />
            Back to Reviews
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">Review Details</h1>
        </div>
        
        <div className="flex gap-2">
          <Link
            to={`/reviews/edit/${review._id}`}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Edit size={16} />
            Edit Review
          </Link>
          <button
            onClick={handleDelete}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <Trash2 size={16} />
            Delete Review
          </button>
        </div>
      </div>

      {/* Review Content */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-6">
          {/* Review Header */}
          <div className="flex items-start gap-6 mb-6">
            {/* Reviewer Image */}
            <div className="flex-shrink-0">
              {review.imageUrl ? (
                <img
                  src={review.imageUrl}
                  alt={review.name}
                  className="w-20 h-20 rounded-full object-cover border-4 border-gray-200"
                />
              ) : (
                <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center">
                  <User size={32} className="text-gray-500" />
                </div>
              )}
            </div>

            {/* Reviewer Info */}
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-2xl font-bold text-gray-900">{review.name}</h2>
                {review.isFeatured && (
                  <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                    <Heart size={14} className="fill-current" />
                    Featured Review
                  </div>
                )}
              </div>

              {/* Rating */}
              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center gap-1">
                  {renderStars(review.star)}
                </div>
                <span className="text-lg font-semibold text-gray-700">
                  {review.star}/5
                </span>
              </div>

              {/* Meta Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <User size={16} className="text-gray-500" />
                  <span className="text-gray-600">Relationship:</span>
                  <span className="font-medium capitalize">
                    {RELATIONSHIP_TYPES.find(type => type.value === review.relationship)?.label || review.relationship}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar size={16} className="text-gray-500" />
                  <span className="text-gray-600">Event Type:</span>
                  <span className="font-medium capitalize">
                    {EVENT_TYPES.find(type => type.value === review.eventType)?.label || review.eventType}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar size={16} className="text-gray-500" />
                  <span className="text-gray-600">Date:</span>
                  <span className="font-medium">
                    {new Date(review.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Review Text */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Review</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-gray-700 leading-relaxed text-lg">
                "{review.review}"
              </p>
            </div>
          </div>

          {/* Additional Information */}
          <div className="border-t pt-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Review Status</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Featured:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      review.isFeatured 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {review.isFeatured ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Rating:</span>
                    <span className="font-medium">{review.star} out of 5 stars</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Timestamps</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Created:</span>
                    <span className="font-medium">
                      {new Date(review.createdAt).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Updated:</span>
                    <span className="font-medium">
                      {new Date(review.updatedAt).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewDetail;
