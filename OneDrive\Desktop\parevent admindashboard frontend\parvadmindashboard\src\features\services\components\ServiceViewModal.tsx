import React, { useState } from 'react';
import { X, FileText, Image, Calendar, Hash, ChevronDown, ChevronUp } from 'lucide-react';
import { Service } from '../types/service';
import SvgIcon from '../../../components/SvgIcon';

interface ServiceViewModalProps {
  service: Service;
  isOpen: boolean;
  onClose: () => void;
}

const ServiceViewModal: React.FC<ServiceViewModalProps> = ({ service, isOpen, onClose }) => {
  const [expandedSteps, setExpandedSteps] = useState<number[]>([]);

  if (!isOpen) return null;

  const toggleStep = (index: number) => {
    setExpandedSteps(prev =>
      prev.includes(index)
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-xs sm:max-w-sm md:max-w-2xl lg:max-w-4xl xl:max-w-5xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-3 sm:p-4 lg:p-6 border-b">
          <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
            <SvgIcon
              src={service.icons}
              alt="Service Icon"
              className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0"
              fallback={
                <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-300 rounded flex items-center justify-center text-xs flex-shrink-0">
                  📄
                </div>
              }
            />
            <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate">{service.title}</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0 ml-2"
          >
            <X size={20} className="sm:w-6 sm:h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-3 sm:p-4 lg:p-6">
          {/* Service Image */}
          <div className="mb-4 sm:mb-6">
            <div className="relative rounded-lg overflow-hidden bg-gray-100">
              <img
                src={service.image}
                alt={service.title}
                className="w-full h-40 sm:h-48 md:h-56 lg:h-64 object-cover"
              />
            </div>
          </div>

          {/* Service Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            {/* Basic Information */}
            <div className="space-y-3 sm:space-y-4">
              <div>
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3">Service Information</h3>
                <div className="space-y-2 sm:space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Title</label>
                    <p className="text-gray-900 font-medium">{service.title}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">Primary Description</label>
                    <p className="text-gray-900">{service.description}</p>
                  </div>
                  
                  {service.description2 && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Secondary Description</label>
                      <p className="text-gray-900">{service.description2}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* How We Do It Section */}
              {service.howWeDoIt && service.howWeDoIt.length > 0 && (
                <div>
                  <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3 flex items-center gap-2">
                    <FileText size={16} className="text-blue-600 sm:w-[18px] sm:h-[18px]" />
                    <span className="text-sm sm:text-base">How We Do It ({service.howWeDoIt.length} steps)</span>
                  </h4>
                  <div className="space-y-1 sm:space-y-2">
                    {service.howWeDoIt.map((step, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                        <button
                          onClick={() => toggleStep(index)}
                          className="w-full px-3 sm:px-4 py-2 sm:py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left transition-colors"
                        >
                          <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                            <span className="bg-blue-600 text-white text-xs font-medium px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full flex-shrink-0">
                              {index + 1}
                            </span>
                            <span className="font-medium text-gray-900 text-sm sm:text-base truncate">
                              {step.title || `Step ${index + 1}`}
                            </span>
                          </div>
                          {expandedSteps.includes(index) ? (
                            <ChevronUp size={14} className="text-gray-500 flex-shrink-0 sm:w-4 sm:h-4" />
                          ) : (
                            <ChevronDown size={14} className="text-gray-500 flex-shrink-0 sm:w-4 sm:h-4" />
                          )}
                        </button>

                        {expandedSteps.includes(index) && step.description && (
                          <div className="px-3 sm:px-4 py-2 sm:py-3 bg-white border-t border-gray-200">
                            <p className="text-gray-900 whitespace-pre-wrap text-sm sm:text-base">{step.description}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Settings & Metadata */}
            <div className="space-y-3 sm:space-y-4">
              <div>
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3">Settings & Metadata</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center gap-2">
                      <Hash size={14} />
                      Sort Order:
                    </span>
                    <span className="font-medium">{service.sortOrder}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      service.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {service.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Files Information */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Files</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                      <Image size={14} />
                      Service Icon (SVG)
                    </label>
                    <div className="mt-2 p-3 bg-gray-50 rounded-lg flex items-center gap-3">
                      <SvgIcon
                        src={service.icons}
                        alt="Service Icon"
                        className="w-8 h-8"
                        fallback={
                          <div className="w-8 h-8 bg-gray-300 rounded flex items-center justify-center text-xs">
                            📄
                          </div>
                        }
                      />
                      <div className="flex-1">
                        <p className="text-xs text-gray-500 break-all">{service.icons}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                      <Image size={14} />
                      Service Image
                    </label>
                    <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                      <p className="text-xs text-gray-500 break-all">{service.image}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Timestamps</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center gap-2">
                      <Calendar size={14} />
                      Created:
                    </span>
                    <span className="font-medium">
                      {new Date(service.createdAt).toLocaleDateString()} {new Date(service.createdAt).toLocaleTimeString()}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center gap-2">
                      <Calendar size={14} />
                      Updated:
                    </span>
                    <span className="font-medium">
                      {new Date(service.updatedAt).toLocaleDateString()} {new Date(service.updatedAt).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Character Counts */}
          <div className="mt-4 sm:mt-6 pt-4 sm:pt-6 border-t">
            <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-3">Content Statistics</h4>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4 text-xs sm:text-sm">
              <div className="bg-gray-50 p-3 rounded-lg text-center">
                <div className="font-medium text-gray-900">{service.description.length}</div>
                <div className="text-gray-600">Primary Desc. Chars</div>
              </div>
              
              {service.description2 && (
                <div className="bg-gray-50 p-3 rounded-lg text-center">
                  <div className="font-medium text-gray-900">{service.description2.length}</div>
                  <div className="text-gray-600">Secondary Desc. Chars</div>
                </div>
              )}
              
              {service.howWeDoIt && service.howWeDoIt.length > 0 && (
                <div className="bg-gray-50 p-3 rounded-lg text-center">
                  <div className="font-medium text-gray-900">{service.howWeDoIt.length}</div>
                  <div className="text-gray-600">How We Do It Steps</div>
                </div>
              )}

              {service.howWeDoIt && service.howWeDoIt.length > 0 && (
                <div className="bg-gray-50 p-3 rounded-lg text-center">
                  <div className="font-medium text-gray-900">
                    {service.howWeDoIt.reduce((total, step) => total + (step.description?.length || 0), 0)}
                  </div>
                  <div className="text-gray-600">Total Step Content</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-3 sm:p-4 lg:p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-3 sm:px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors text-sm sm:text-base"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServiceViewModal;
