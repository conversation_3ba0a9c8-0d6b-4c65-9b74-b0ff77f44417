import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Eye, Edit, Trash2, Plus, Search, Filter, MapPin, Users } from 'lucide-react';
import { Venue, VenueFilters, VENUE_TYPES } from '../types/venue';
import { venuesApi } from '../api/venuesApi';
import VenueViewModal from './VenueViewModal';

const VenuesList: React.FC = () => {
  const [venues, setVenues] = useState<Venue[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<VenueFilters>({
    page: 1,
    limit: 12,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
  const [totalPages, setTotalPages] = useState(1);
  const [searchTitle, setSearchTitle] = useState('');
  const [selectedVenue, setSelectedVenue] = useState<Venue | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  useEffect(() => {
    fetchVenues();
  }, [filters]);

  const fetchVenues = async () => {
    try {
      setLoading(true);
      const response = await venuesApi.getAll(filters);
      console.log('Venues API Response:', response);

      if (response.success) {
        // Handle different response structures
        const data = response.data;
        let venueItems = [];

        console.log('Venues response data:', data);

        if (Array.isArray(data)) {
          venueItems = data;
        } else if (data && Array.isArray(data.venues)) {
          // Backend returns data.venues array
          venueItems = data.venues;
        } else if (data && Array.isArray(data.items)) {
          venueItems = data.items;
        } else if (data && Array.isArray(data.data)) {
          venueItems = data.data;
        } else {
          venueItems = [];
        }

        console.log('Processed venue items:', venueItems);
        setVenues(venueItems);

        if (data && data.pagination) {
          setTotalPages(data.pagination.totalPages);
        }
      } else {
        setVenues([]);
      }
    } catch (error) {
      console.error('Error fetching venues:', error);
      toast.error('Failed to fetch venues');
      setVenues([]);
    } finally {
      setLoading(false);
    }
  };

  const handleView = (venue: Venue) => {
    setSelectedVenue(venue);
    setIsViewModalOpen(true);
  };

  const handleCloseViewModal = () => {
    setSelectedVenue(null);
    setIsViewModalOpen(false);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this venue?')) {
      return;
    }

    try {
      const response = await venuesApi.delete(id);
      if (response.success) {
        toast.success('Venue deleted successfully');
        fetchVenues();
      }
    } catch (error) {
      console.error('Error deleting venue:', error);
      toast.error('Failed to delete venue');
    }
  };

  const handleSearch = () => {
    // For title search, we'll filter on the frontend since the API doesn't have title search
    fetchVenues();
  };

  const handleFilterChange = (key: keyof VenueFilters, value: any) => {
    setFilters(prev => ({ 
      ...prev, 
      [key]: value === 'all' || value === '' ? undefined : value, 
      page: 1 
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const filteredVenues = searchTitle && Array.isArray(venues)
    ? venues.filter(venue =>
        venue.name.toLowerCase().includes(searchTitle.toLowerCase()) ||
        venue.location.toLowerCase().includes(searchTitle.toLowerCase())
      )
    : Array.isArray(venues) ? venues : [];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Venues Management</h1>
        <Link
          to="/venues/create"
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
        >
          <Plus size={20} />
          Add Venue
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div className="flex items-center gap-2">
            <input
              type="text"
              placeholder="Search venues..."
              value={searchTitle}
              onChange={(e) => setSearchTitle(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-full"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button
              onClick={handleSearch}
              className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg"
            >
              <Search size={16} />
            </button>
          </div>

          {/* Venue Type Filter */}
          <div className="flex items-center gap-2">
            <Filter size={16} className="text-gray-500" />
            <select
              value={filters.venueType || 'all'}
              onChange={(e) => handleFilterChange('venueType', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-full"
            >
              <option value="all">All Types</option>
              {VENUE_TYPES.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* City Filter */}
          <div>
            <input
              type="text"
              placeholder="Filter by city..."
              value={filters.city || ''}
              onChange={(e) => handleFilterChange('city', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-full"
            />
          </div>

          {/* Capacity Filter */}
          <div>
            <input
              type="number"
              placeholder="Min capacity..."
              value={filters.minCapacity || ''}
              onChange={(e) => handleFilterChange('minCapacity', e.target.value ? parseInt(e.target.value) : undefined)}
              className="border border-gray-300 rounded-lg px-3 py-2 w-full"
            />
          </div>
        </div>
      </div>

      {/* Venues Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.isArray(filteredVenues) && filteredVenues.map((venue) => (
          <div key={venue._id} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="relative">
              <img
                src={venue.imageUrl || venue.image}
                alt={venue.name}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                {VENUE_TYPES.find(t => t.value === venue.venueType)?.label}
              </div>
            </div>
            <div className="p-4">
              <h3 className="font-bold text-lg mb-2 truncate">{venue.name}</h3>

              <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                <MapPin size={14} />
                <span className="truncate">{venue.location}</span>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                <div className="flex items-center gap-1">
                  <Users size={14} />
                  <span>{venue.capacity} capacity</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users size={14} />
                  <span>{venue.seats} seats</span>
                </div>
              </div>



              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <button
                    onClick={() => handleView(venue)}
                    className="text-blue-500 hover:text-blue-600"
                    title="View"
                  >
                    <Eye size={16} />
                  </button>
                  <Link
                    to={`/venues/edit/${venue._id}`}
                    className="text-green-500 hover:text-green-600"
                    title="Edit"
                  >
                    <Edit size={16} />
                  </Link>
                  <button
                    onClick={() => handleDelete(venue._id)}
                    className="text-red-500 hover:text-red-600"
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
                <span className="text-xs text-gray-500">
                  {new Date(venue.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex gap-2">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 rounded-lg ${
                  filters.page === page
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
        </div>
      )}

      {(!Array.isArray(filteredVenues) || filteredVenues.length === 0) && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No venues found</p>
          <Link
            to="/venues/create"
            className="text-blue-500 hover:text-blue-600 mt-2 inline-block"
          >
            Create your first venue
          </Link>
        </div>
      )}

      {/* View Modal */}
      {selectedVenue && (
        <VenueViewModal
          venue={selectedVenue}
          isOpen={isViewModalOpen}
          onClose={handleCloseViewModal}
        />
      )}
    </div>
  );
};

export default VenuesList;
