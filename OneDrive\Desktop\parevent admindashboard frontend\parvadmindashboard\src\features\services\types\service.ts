// Simplified Services types based on API documentation

export interface HowWeDoItStep {
  title?: string;
  description?: string;
}

export interface Service {
  _id: string;
  title: string;
  description: string;
  description2?: string;
  icons: string;
  image: string;
  howWeDoIt?: HowWeDoItStep[];
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateServiceRequest {
  title: string;
  description: string;
  description2?: string;
  howWeDoIt?: HowWeDoItStep[];
  sortOrder?: number;
  icons: File;
  image: File;
}

export interface UpdateServiceRequest {
  title?: string;
  description?: string;
  description2?: string;
  howWeDoIt?: HowWeDoItStep[];
  sortOrder?: number;
  icons?: File;
  image?: File;
}

export interface ServiceFilters {
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ServiceStats {
  totalServices: number;
  serviceTypeStats: Array<{
    _id: string;
    count: number;
  }>;
  categoryStats: Array<{
    _id: string;
    count: number;
  }>;
  featuredCount: number;
  recentAdditions: number;
  mostViewed: Array<{
    _id: string;
    title: string;
    views: number;
  }>;
  totalViews: number;
}
