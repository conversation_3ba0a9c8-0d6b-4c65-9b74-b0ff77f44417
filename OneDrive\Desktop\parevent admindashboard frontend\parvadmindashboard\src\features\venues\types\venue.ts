// Simplified Venue types based on updated API documentation

export interface Venue {
  _id: string;
  name: string;
  image: string;
  imageUrl?: string;
  venueType: VenueType;
  location: string;
  capacity: number;
  seats: number;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateVenueRequest {
  name: string;
  venueType: VenueType;
  location: string;
  capacity: number;
  seats: number;
  sortOrder?: number;
  image: File;
}

export interface UpdateVenueRequest {
  name?: string;
  venueType?: VenueType;
  location?: string;
  capacity?: number;
  seats?: number;
  sortOrder?: number;
  image?: File;
}

export interface VenueFilters {
  venueType?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Venue Types
export type VenueType =
  | 'banquet-hall'
  | 'outdoor'
  | 'resort'
  | 'hotel'
  | 'farmhouse'
  | 'palace'
  | 'garden'
  | 'beach'
  | 'other';

export const VENUE_TYPES: { value: VenueType; label: string }[] = [
  { value: 'banquet-hall', label: 'Banquet Hall' },
  { value: 'outdoor', label: 'Outdoor' },
  { value: 'resort', label: 'Resort' },
  { value: 'hotel', label: 'Hotel' },
  { value: 'farmhouse', label: 'Farmhouse' },
  { value: 'palace', label: 'Palace' },
  { value: 'garden', label: 'Garden' },
  { value: 'beach', label: 'Beach' },
  { value: 'other', label: 'Other' },
];

// No additional constants needed for simplified venue system
