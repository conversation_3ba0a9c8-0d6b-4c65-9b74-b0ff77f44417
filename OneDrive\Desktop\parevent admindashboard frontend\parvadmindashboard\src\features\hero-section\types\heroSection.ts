export interface HeroSection {
  _id: string;
  title: string;
  subtitle?: string;
  description?: string;
  image: string; // S3 URL
  buttonText: string;
  buttonLink: string;
  isActive: boolean;
  isPrimary: boolean;
  sortOrder: number;
  displayDuration: number; // 1000-30000ms
  createdAt: string;
  updatedAt: string;
}

export interface CreateHeroSectionRequest {
  title: string;
  subtitle?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  isPrimary?: boolean;
  displayDuration?: number;
  isActive?: boolean;
  sortOrder?: number;
  image: File; // Required hero image
}

export interface UpdateHeroSectionRequest {
  title?: string;
  subtitle?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  isPrimary?: boolean;
  displayDuration?: number;
  isActive?: boolean;
  sortOrder?: number;
  image?: File; // Optional new image
}

export interface HeroSectionFilters {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  activeOnly?: boolean;
}

export interface HeroSectionResponse {
  success: boolean;
  data: {
    heroSections: HeroSection[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
  message?: string;
}

export interface SingleHeroSectionResponse {
  success: boolean;
  data: HeroSection;
  message?: string;
}
