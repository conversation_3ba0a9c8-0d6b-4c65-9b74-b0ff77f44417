export interface HeroSection {
  _id: string;
  title: string;
  subtitle: string;
  description: string;
  buttonText: string;
  buttonLink: string;
  isPrimary: boolean;
  displayDuration: number;
  image: string;
  images?: string[]; // Array of 3 images
  imageUrls?: string[]; // Alternative field name
  activeImageIndex: number; // Which image is active (0, 1, or 2)
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateHeroSectionRequest {
  title: string;
  subtitle: string;
  description: string;
  buttonText: string;
  buttonLink: string;
  isPrimary: boolean;
  displayDuration: number;
  isActive?: boolean;
  sortOrder?: number;
  image: File;
  images?: File[]; // Max 3 images
  activeImageIndex?: number; // Which image is active (default 0)
}

export interface UpdateHeroSectionRequest {
  title?: string;
  subtitle?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  isPrimary?: boolean;
  displayDuration?: number;
  isActive?: boolean;
  sortOrder?: number;
  image?: File;
  images?: File[]; // Max 3 images
  activeImageIndex?: number; // Which image is active
}

export interface HeroSectionFilters {
  isPrimary?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
