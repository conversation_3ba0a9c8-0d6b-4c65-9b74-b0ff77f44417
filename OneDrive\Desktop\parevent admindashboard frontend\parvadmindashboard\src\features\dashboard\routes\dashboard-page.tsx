// /pages/DashboardPage.tsx
import React, { useState, useEffect } from "react";
import CardComponent from "../components/card";
import LineChart from "../components/chart";
import MostSubscribedChannels from "../components/MostSubscribed";
import { BarChartComponent } from "../components/bar-graph";
import { AreaChartComponent } from "../components/area-chart";
import { Loader } from "@/components/globalfiles/loader";
import { toast } from "react-toastify";

// Import API functions
import { getAllServices } from "@/features/servicesfolder/api/serviceApi";
import { getAllBlogs } from "@/features/blog/components/api/api";
import { getAllContacts, getContactStatistics } from "@/features/contact/components/api/api";
import { getCommentStatistics } from "@/features/postComments/api/commentApi";
// New APIs for dashboard restructure
import { galleryApi } from "@/features/gallery/api/galleryApi";
import { reviewsApi } from "@/features/reviews/api/reviewsApi";
import { heroSectionApi } from "@/features/hero-section/api/heroSectionApi";
import { venuesApi } from "@/features/venues/api/venuesApi";
import { venueBookingsApi } from "@/features/venue-bookings/api/venueBookingsApi";

// Dashboard Statistics Interface
interface DashboardStats {
  // New primary stats as requested
  totalGalleryPhotos: number;
  totalReviews: number;
  totalHeroSections: number;
  totalVenues: number;
  totalVenueBookings: number;
  totalBlogs: number;
  totalServices: number;
  totalContacts: number;
  totalComments: number;

  // Additional stats
  activeVenues: number;
  newVenueBookings: number;
  pendingContacts: number;
  activeHeroSections: number;
  approvedReviews: number;
  pendingComments: number;
}

const DashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    // New primary stats as requested
    totalGalleryPhotos: 0,
    totalReviews: 0,
    totalHeroSections: 0,
    totalVenues: 0,
    totalVenueBookings: 0,
    totalBlogs: 0,
    totalServices: 0,
    totalContacts: 0,
    totalComments: 0,

    // Additional stats
    activeVenues: 0,
    newVenueBookings: 0,
    pendingContacts: 0,
    activeHeroSections: 0,
    approvedReviews: 0,
    pendingComments: 0,
  });
  const [loading, setLoading] = useState(true);

  const [contactStats, setContactStats] = useState<any[]>([]);
  const [recentProjects, setRecentProjects] = useState<any[]>([]);

  // Fetch all dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // Fetch all data in parallel
        const [
          galleryResponse,
          reviewsResponse,
          heroSectionsResponse,
          venuesResponse,
          venueBookingsResponse,
          blogsResponse,
          servicesResponse,
          contactsResponse,
          contactStatsResponse,
          commentStatsResponse
        ] = await Promise.all([
          galleryApi.getAll({ page: 1, limit: 100 }),
          reviewsApi.getAll({ page: 1, limit: 100 }),
          heroSectionApi.getAll({ page: 1, limit: 100 }),
          venuesApi.getAll({ page: 1, limit: 100 }),
          venueBookingsApi.getAll({ page: 1, limit: 100 }),
          getAllBlogs({ page: 1, pageSize: 100 }),
          getAllServices({ page: 1, limit: 100 }),
          getAllContacts({ page: 1, limit: 100 }),
          getContactStatistics(),
          getCommentStatistics()
        ]);

        // Process new dashboard data
        const galleryData = galleryResponse.success ? ((galleryResponse.data as any)?.galleries || (galleryResponse.data as any)?.data || []) : [];
        const reviewsData = reviewsResponse.success ? ((reviewsResponse.data as any)?.reviews || (reviewsResponse.data as any)?.data || []) : [];
        const heroSectionsData = heroSectionsResponse.success ? (heroSectionsResponse.data?.heroSections || []) : [];
        const venuesData = venuesResponse.success ? ((venuesResponse.data as any)?.venues || (venuesResponse.data as any)?.data || []) : [];
        const venueBookingsData = venueBookingsResponse.success ? ((venueBookingsResponse.data as any)?.bookings || (venueBookingsResponse.data as any)?.venueBookings || (venueBookingsResponse.data as any)?.data || []) : [];

        // Process existing data
        const blogsData = (blogsResponse.data as any)?.blogs || (blogsResponse.data as any)?.data || [];
        const servicesData = (servicesResponse.data as any)?.services || [];
        const contactsData = (contactsResponse.data as any)?.data || [];
        const contactStatsData = (contactStatsResponse.data as any) || {};
        const commentStats = commentStatsResponse.status ? commentStatsResponse.data : null;

        // Calculate stats from new data
        const activeVenuesCount = Array.isArray(venuesData) ? venuesData.filter((v: any) => v.isActive).length : 0;
        const activeHeroSectionsCount = Array.isArray(heroSectionsData) ? heroSectionsData.filter((h: any) => h.isActive).length : 0;
        const approvedReviewsCount = Array.isArray(reviewsData) ? reviewsData.filter((r: any) => r.status === 'approved').length : 0;
        const newVenueBookingsCount = Array.isArray(venueBookingsData) ? venueBookingsData.filter((b: any) => b.status === 'new').length : 0;

        setStats({
          // New primary stats as requested
          totalGalleryPhotos: Array.isArray(galleryData) ? galleryData.length : 0,
          totalReviews: Array.isArray(reviewsData) ? reviewsData.length : 0,
          totalHeroSections: Array.isArray(heroSectionsData) ? heroSectionsData.length : 0,
          totalVenues: Array.isArray(venuesData) ? venuesData.length : 0,
          totalVenueBookings: Array.isArray(venueBookingsData) ? venueBookingsData.length : 0,
          totalBlogs: Array.isArray(blogsData) ? blogsData.length : 0,
          totalServices: Array.isArray(servicesData) ? servicesData.length : 0,
          totalContacts: (contactStatsData as any)?.totalContacts || (Array.isArray(contactsData) ? contactsData.length : 0),
          totalComments: commentStats?.totalComments || 0,

          // Additional stats
          activeVenues: activeVenuesCount,
          newVenueBookings: newVenueBookingsCount,
          pendingContacts: (contactStatsData as any)?.statusStats?.find((s: any) => s._id === 'pending')?.count || 0,
          activeHeroSections: activeHeroSectionsCount,
          approvedReviews: approvedReviewsCount,
          pendingComments: commentStats?.pendingComments || 0,
        });

        // Set chart data
        setContactStats((contactStatsData as any)?.statusStats || []);
        setRecentProjects(Array.isArray(venueBookingsData) ? venueBookingsData.slice(0, 5) : []);

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Failed to load dashboard data');

        // Set some default values if API fails
        setStats({
          totalGalleryPhotos: 0,
          totalReviews: 0,
          totalHeroSections: 0,
          totalVenues: 0,
          totalVenueBookings: 0,
          totalBlogs: 0,
          totalServices: 0,
          totalContacts: 0,
          totalComments: 0,
          activeVenues: 0,
          newVenueBookings: 0,
          pendingContacts: 0,
          activeHeroSections: 0,
          approvedReviews: 0,
          pendingComments: 0,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Create dynamic card data based on real stats - as requested
  const dynamicCardData = [
    {
      title: "Total Gallery Photos",
      count: stats.totalGalleryPhotos,
      bgImage: "/assets/one-user.png",
    },
    {
      title: "Total Reviews",
      count: stats.totalReviews,
      bgImage: "/assets/active.png",
    },
    {
      title: "Total Hero Sections",
      count: stats.totalHeroSections,
      bgImage: "/assets/Group.png",
    },
    {
      title: "Total Venues",
      count: stats.totalVenues,
      bgImage: "/assets/user.png",
    },
    {
      title: "Venue Bookings",
      count: stats.totalVenueBookings,
      bgImage: "/assets/vdeo.png",
    },
    {
      title: "Blog Posts",
      count: stats.totalBlogs,
      bgImage: "/assets/subscibed.png",
    },
    {
      title: "Total Services",
      count: stats.totalServices,
      bgImage: "/assets/revenue.png",
    },
    {
      title: "Contact Us",
      count: stats.totalContacts,
      bgImage: "/assets/revenue.png",
    },
    {
      title: "Post Comments",
      count: stats.totalComments,
      bgImage: "/assets/revenue.png",
    },
  ];

  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Loader />
      </div>
    );
  }

  return (
    <div className="flex h-screen w-full">
      <div className="flex flex-col flex-grow">
        <main className="p-6 flex-grow overflow-y-auto">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Dashboard Overview</h1>
            <p className="text-gray-600 mt-1">Welcome to your admin dashboard</p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 p-4">
            {dynamicCardData.map((card, index) => (
              <CardComponent
                key={index}
                title={card.title}
                count={card.count}
                bgImage={card.bgImage}
              />
            ))}
          </div>

          {/* Charts Row 1 */}
          <div className="mt-6 flex flex-col lg:flex-row gap-6">
            <div className="flex-1 shadow-xl bg-white rounded-lg p-4">
              <p className="font-bold mb-4">Venue Types Distribution</p>
              <LineChart />
            </div>
            <div className="flex-1 shadow-xl bg-white rounded-lg p-4">
              <p className="font-bold mb-4">Recent Venue Bookings</p>
              <MostSubscribedChannels
                channels={recentProjects.map((booking: any) => ({
                  name: booking.venueName || booking.fullName || `Booking ${booking._id}`,
                  subscribers: 1,
                  logo: ""
                }))}
              />
            </div>
          </div>

          {/* Charts Row 2 */}
          <div className="mt-6 flex flex-col lg:flex-row gap-6">
            {/* Contact Status Distribution */}
            <div className="flex-1 shadow-xl bg-white rounded-lg p-4">
              <p className="font-bold mb-4">Booking Status Distribution</p>
              <BarChartComponent
                data={contactStats.map((stat: any) => ({
                  name: stat._id || 'Unknown',
                  value: stat.count || 0
                }))}
              />
            </div>

            {/* Monthly Growth */}
            <div className="flex-1 shadow-xl bg-white rounded-lg p-4">
              <p className="font-bold mb-4">Monthly Venue Bookings Growth</p>
              <AreaChartComponent />
            </div>
          </div>

          {/* Quick Stats Summary */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg">
              <h3 className="text-lg font-semibold">Active Venues</h3>
              <p className="text-2xl font-bold">{stats.activeVenues}</p>
              <p className="text-sm opacity-90">Currently active</p>
            </div>
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
              <h3 className="text-lg font-semibold">New Bookings</h3>
              <p className="text-2xl font-bold">{stats.newVenueBookings}</p>
              <p className="text-sm opacity-90">Awaiting review</p>
            </div>
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
              <h3 className="text-lg font-semibold">Active Hero Sections</h3>
              <p className="text-2xl font-bold">{stats.activeHeroSections}</p>
              <p className="text-sm opacity-90">Currently live</p>
            </div>
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4 rounded-lg">
              <h3 className="text-lg font-semibold">Approved Reviews</h3>
              <p className="text-2xl font-bold">{stats.approvedReviews}</p>
              <p className="text-sm opacity-90">Published reviews</p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardPage;
