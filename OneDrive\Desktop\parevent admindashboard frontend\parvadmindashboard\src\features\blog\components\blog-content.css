/* Blog Content Display Styles - Matches Tiptap Editor Output */

.blog-content {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #374151;
}

/* Headings */
.blog-content h1 {
  font-size: 1.875rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem 0;
  line-height: 1.2;
  color: #1f2937;
}

.blog-content h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1.5rem 0 1rem 0;
  line-height: 1.3;
  color: #1f2937;
}

.blog-content h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin: 1.25rem 0 0.75rem 0;
  line-height: 1.4;
  color: #1f2937;
}

/* Paragraphs */
.blog-content p {
  margin: 0.75rem 0;
  word-break: break-word;
  overflow-wrap: break-word;
  color: #374151;
}

/* Lists */
.blog-content ul.tiptap-bullet-list,
.blog-content ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.blog-content ol.tiptap-ordered-list,
.blog-content ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.blog-content li.tiptap-list-item,
.blog-content li {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.blog-content li p {
  margin: 0.25rem 0;
}

/* Nested lists */
.blog-content ul ul,
.blog-content ol ol,
.blog-content ul ol,
.blog-content ol ul {
  margin: 0.25rem 0;
}

/* Strong and emphasis */
.blog-content strong {
  font-weight: bold;
  color: #1f2937;
}

.blog-content em {
  font-style: italic;
}

/* Links */
.blog-content a {
  color: #3b82f6;
  text-decoration: underline;
  text-decoration-color: #3b82f6;
}

.blog-content a:hover {
  color: #1d4ed8;
  text-decoration-color: #1d4ed8;
}

/* Blockquotes */
.blog-content blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #6b7280;
}

/* Code */
.blog-content code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace;
  font-size: 0.875em;
}

.blog-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace;
}

.blog-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* Tables */
.blog-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1.5rem 0;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  overflow: hidden;
}

.blog-content th,
.blog-content td {
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  text-align: left;
}

.blog-content th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

/* Horizontal rule */
.blog-content hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 2rem 0;
}

/* Images */
.blog-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

/* Task lists */
.blog-content ul[data-type="taskList"] {
  list-style: none;
  padding: 0;
}

.blog-content ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin: 0.5rem 0;
}

.blog-content ul[data-type="taskList"] li input[type="checkbox"] {
  margin-right: 0.5rem;
  margin-top: 0.125rem;
}

/* Highlights */
.blog-content mark {
  background-color: #fef08a;
  padding: 0.125rem 0.25rem;
  border-radius: 0.125rem;
}

/* Text alignment */
.blog-content .has-text-align-left {
  text-align: left;
}

.blog-content .has-text-align-center {
  text-align: center;
}

.blog-content .has-text-align-right {
  text-align: right;
}

.blog-content .has-text-align-justify {
  text-align: justify;
}

/* Subscript and superscript */
.blog-content sub {
  vertical-align: sub;
  font-size: smaller;
}

.blog-content sup {
  vertical-align: super;
  font-size: smaller;
}

/* Underline */
.blog-content u {
  text-decoration: underline;
  text-decoration-color: #3b82f6;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}

/* Ensure proper spacing and readability */
.blog-content > *:first-child {
  margin-top: 0;
}

.blog-content > *:last-child {
  margin-bottom: 0;
}

/* Color preservation for inline styles */
.blog-content span[style*="color"] {
  /* Preserve inline color styles */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-content {
    font-size: 0.9rem;
  }
  
  .blog-content h1 {
    font-size: 1.5rem;
  }
  
  .blog-content h2 {
    font-size: 1.25rem;
  }
  
  .blog-content h3 {
    font-size: 1.125rem;
  }
  
  .blog-content table {
    font-size: 0.875rem;
  }
  
  .blog-content th,
  .blog-content td {
    padding: 0.5rem;
  }
}
