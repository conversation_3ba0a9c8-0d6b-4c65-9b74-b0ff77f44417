import React from 'react';
import { X, MapPin, Users, Calendar, Hash, Building } from 'lucide-react';
import { Venue, VENUE_TYPES } from '../types/venue';

interface VenueViewModalProps {
  venue: Venue;
  isOpen: boolean;
  onClose: () => void;
}

const VenueViewModal: React.FC<VenueViewModalProps> = ({ venue, isOpen, onClose }) => {
  if (!isOpen) return null;

  const venueTypeLabel = VENUE_TYPES.find(type => type.value === venue.venueType)?.label || venue.venueType;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <Building className="text-blue-600" size={24} />
            <h2 className="text-2xl font-bold text-gray-900">{venue.name}</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Venue Image */}
          <div className="mb-6">
            <div className="relative rounded-lg overflow-hidden bg-gray-100">
              <img
                src={venue.imageUrl || venue.image}
                alt={venue.name}
                className="w-full h-64 object-cover"
              />
              <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                {venueTypeLabel}
              </div>
            </div>
          </div>

          {/* Venue Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Venue Information</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Venue Name</label>
                    <p className="text-gray-900 font-medium">{venue.name}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">Venue Type</label>
                    <p className="text-gray-900">{venueTypeLabel}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                      <MapPin size={14} />
                      Location
                    </label>
                    <p className="text-gray-900">{venue.location}</p>
                  </div>
                </div>
              </div>

              {/* Capacity Information */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Capacity Details</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Users className="text-blue-600" size={20} />
                    </div>
                    <div className="text-2xl font-bold text-blue-600">{venue.capacity}</div>
                    <div className="text-sm text-gray-600">Total Capacity</div>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-lg text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Users className="text-green-600" size={20} />
                    </div>
                    <div className="text-2xl font-bold text-green-600">{venue.seats}</div>
                    <div className="text-sm text-gray-600">Seating Capacity</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Settings & Metadata */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Settings & Status</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center gap-2">
                      <Hash size={14} />
                      Sort Order:
                    </span>
                    <span className="font-medium">{venue.sortOrder}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      venue.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {venue.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Image Information */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Image Information</h4>
                <div className="space-y-2">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Image URL:</label>
                    <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                      <p className="text-xs text-gray-500 break-all">
                        {venue.imageUrl || venue.image}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Timestamps</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center gap-2">
                      <Calendar size={14} />
                      Created:
                    </span>
                    <span className="font-medium">
                      {new Date(venue.createdAt).toLocaleDateString()} {new Date(venue.createdAt).toLocaleTimeString()}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center gap-2">
                      <Calendar size={14} />
                      Updated:
                    </span>
                    <span className="font-medium">
                      {new Date(venue.updatedAt).toLocaleDateString()} {new Date(venue.updatedAt).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Venue Statistics */}
          <div className="mt-6 pt-6 border-t">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Venue Statistics</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-gray-900">{venue.capacity}</div>
                <div className="text-sm text-gray-600">Total Capacity</div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-gray-900">{venue.seats}</div>
                <div className="text-sm text-gray-600">Seating Capacity</div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-gray-900">{Math.round((venue.seats / venue.capacity) * 100)}%</div>
                <div className="text-sm text-gray-600">Seating Ratio</div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-gray-900">{venue.sortOrder}</div>
                <div className="text-sm text-gray-600">Display Order</div>
              </div>
            </div>
          </div>

          {/* Venue Details Summary */}
          <div className="mt-6 pt-6 border-t">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Venue Summary</h4>
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-gray-900">
                <strong>{venue.name}</strong> is a {venueTypeLabel.toLowerCase()} located at {venue.location}. 
                This venue can accommodate up to <strong>{venue.capacity} guests</strong> with seating for <strong>{venue.seats} people</strong>. 
                {venue.isActive ? 'This venue is currently active and available for bookings.' : 'This venue is currently inactive.'}
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default VenueViewModal;
