"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useState, useEffect } from "react";
import { updateTeam } from "../api/api";
import { toast } from "react-toastify";
import {
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TeamType } from "../../type/teamType";

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name cannot exceed 100 characters"),
  sortOrder: z.number().min(1, "Sort order must be at least 1").default(1),
});

type FormData = z.infer<typeof formSchema>;

interface EditTeamModalProps {
  team: TeamType;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onSuccess: () => void;
}

function EditTeamModal({ team, setIsOpen, onSuccess }: EditTeamModalProps) {
  console.log("EditTeamModal received team data:", team);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: team.name,
      sortOrder: team.sortOrder || 1,
    }
  });
  
  const [preview, setPreview] = useState<string | null>(team.imageUrl || null);
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [message, setMessage] = useState("");

  // Set initial form values when team data changes
  useEffect(() => {
    if (team) {
      setValue("name", team.name);
      setValue("sortOrder", team.sortOrder || 1);
      setPreview(team.imageUrl || null);
    }
  }, [team, setValue]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setProfileImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };



  const onSubmit = async (data: FormData) => {
    setFormLoading(true);
    setMessage("");

    try {
      console.log("Updating team:", team);
      console.log("Team _id:", team._id);
      console.log("Team fixedId:", team.fixedId);

      // Use the _id for the new API (no more fixedId)
      const teamId = team._id;
      console.log("Using team ID:", teamId);

      if (!teamId) {
        throw new Error("Team ID is missing");
      }

      const response = await updateTeam({
        id: teamId,
        name: data.name,
        sortOrder: data.sortOrder,
        image: profileImage,
      });

      if (!response.success && !response.status) {
        setMessage(response.error || "Failed to update team member");
        toast.error(response.error || "Failed to update team member");
      } else {
        toast.success("Team member updated successfully!");
        setPreview(null);
        setProfileImage(null);
        reset();
        setIsOpen(false);
        onSuccess(); // Refresh the team list
      }
    } catch (error) {
      console.error("Error updating team member:", error);
      setMessage("An unexpected error occurred");
      toast.error("An unexpected error occurred");
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Edit Team Member</DialogTitle>
      </DialogHeader>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Profile Image Upload */}
        <div className="space-y-2">
          <Label htmlFor="image">Profile Image</Label>
          <div className="flex items-center space-x-4">
            {preview && (
              <img
                src={preview}
                alt="Preview"
                className="w-20 h-20 object-cover rounded-full border"
              />
            )}
            <Input
              id="image"
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="flex-1"
            />
          </div>
        </div>

        {/* Name */}
        <div className="space-y-2">
          <Label htmlFor="name">Name *</Label>
          <Input
            id="name"
            {...register("name")}
            placeholder="Enter team member name"
          />
          {errors.name && (
            <p className="text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        {/* Sort Order */}
        <div className="space-y-2">
          <Label htmlFor="sortOrder">Sort Order</Label>
          <Input
            id="sortOrder"
            type="number"
            min="1"
            {...register("sortOrder", {
              valueAsNumber: true,
              min: { value: 1, message: "Sort order must be at least 1" }
            })}
            placeholder="Enter sort order"
          />
          {errors.sortOrder && (
            <p className="text-sm text-red-600">{errors.sortOrder.message}</p>
          )}
        </div>





        {message && (
          <div className="text-red-600 text-sm">{message}</div>
        )}

        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={formLoading}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={formLoading}>
            {formLoading ? "Updating..." : "Update Team Member"}
          </Button>
        </div>
      </form>
    </DialogContent>
  );
}

export default EditTeamModal;
