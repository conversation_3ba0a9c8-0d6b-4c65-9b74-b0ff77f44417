import axios from 'axios';
import { reviewsEndpoints } from '@/globalurl/baseurl';
import { CreateReviewRequest, UpdateReviewRequest, ReviewFilters, ReviewStats } from '../types/review';
import { mockReviews, mockReviewStats } from '@/utils/mockData';

export const reviewsApi = {
  // Get all reviews with optional filters
  getAll: async (filters?: ReviewFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.relationship) params.append('relationship', filters.relationship);
      if (filters?.eventType) params.append('eventType', filters.eventType);
      if (filters?.star) params.append('star', filters.star.toString());
      if (filters?.isFeatured !== undefined) params.append('isFeatured', filters.isFeatured.toString());
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);

      const url = params.toString() ? `${reviewsEndpoints.getAll}?${params}` : reviewsEndpoints.getAll;
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.warn('Reviews API not available, using mock data:', error);
      return {
        success: true,
        data: mockReviews,
        message: 'Mock data loaded'
      };
    }
  },

  // Get review statistics
  getStats: async (): Promise<{ success: boolean; data: ReviewStats }> => {
    try {
      const response = await axios.get(reviewsEndpoints.getStats);
      return response.data;
    } catch (error) {
      console.warn('Review stats API not available, using mock data:', error);
      return {
        success: true,
        data: mockReviewStats
      };
    }
  },

  // Get review by ID or fixedId
  getById: async (id: string) => {
    try {
      const response = await axios.get(reviewsEndpoints.getById(id));
      return response.data;
    } catch (error: any) {
      console.error('Review getById error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch review',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Create new review
  create: async (data: CreateReviewRequest) => {
    try {
      const formData = new FormData();

      formData.append('name', data.name);
      formData.append('relationship', data.relationship);
      formData.append('review', data.review);
      formData.append('star', data.star.toString());
      formData.append('eventType', data.eventType);
      formData.append('isFeatured', data.isFeatured.toString());
      if (data.image) {
        formData.append('image', data.image);
      }

      const response = await axios.post(reviewsEndpoints.create, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Review create error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to create review',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Update review
  update: async (id: string, data: UpdateReviewRequest) => {
    const formData = new FormData();
    if (data.name) formData.append('name', data.name);
    if (data.relationship) formData.append('relationship', data.relationship);
    if (data.review) formData.append('review', data.review);
    if (data.star !== undefined) formData.append('star', data.star.toString());
    if (data.eventType) formData.append('eventType', data.eventType);
    if (data.isFeatured !== undefined) formData.append('isFeatured', data.isFeatured.toString());
    if (data.image) formData.append('image', data.image);

    const response = await axios.put(reviewsEndpoints.update(id), formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Delete review
  delete: async (id: string) => {
    const response = await axios.delete(reviewsEndpoints.delete(id));
    return response.data;
  },
};
