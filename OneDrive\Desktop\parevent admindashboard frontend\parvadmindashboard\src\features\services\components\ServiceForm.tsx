import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ArrowLeft, Upload, X, ArrowRight, ArrowLeft as BackIcon, Plus } from 'lucide-react';
import { Service, CreateServiceRequest, UpdateServiceRequest, HowWeDoItStep } from '../types/service';
import { servicesApi } from '../api/servicesApi';

interface ServiceFormProps {
  mode: 'create' | 'edit';
}

const ServiceForm: React.FC<ServiceFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [service, setService] = useState<Service | null>(null);
  const [currentPhase, setCurrentPhase] = useState(1);
  const [iconPreview, setIconPreview] = useState<string>('');
  const [imagePreview, setImagePreview] = useState<string>('');

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    description2: '',
    howWeDoIt: [] as HowWeDoItStep[],
    sortOrder: 1,
    icons: null as File | null,
    image: null as File | null,
  });

  useEffect(() => {
    if (mode === 'edit' && id) {
      fetchService();
    }
  }, [mode, id]);

  const fetchService = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await servicesApi.getById(id);
      if (response.success) {
        const serviceData = response.data;
        setService(serviceData);
        setFormData({
          title: serviceData.title,
          description: serviceData.description,
          description2: serviceData.description2 || '',
          howWeDoIt: serviceData.howWeDoIt || [],
          sortOrder: serviceData.sortOrder,
          icons: null,
          image: null,
        });
        setIconPreview(serviceData.icons);
        setImagePreview(serviceData.image);
      } else {
        toast.error('Service not found');
        navigate('/services');
      }
    } catch (error) {
      console.error('Error fetching service:', error);
      toast.error('Failed to fetch service data');
      navigate('/services');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const addHowWeDoItStep = () => {
    setFormData(prev => ({
      ...prev,
      howWeDoIt: [...prev.howWeDoIt, { title: '', description: '' }]
    }));
  };

  const removeHowWeDoItStep = (index: number) => {
    setFormData(prev => ({
      ...prev,
      howWeDoIt: prev.howWeDoIt.filter((_, i) => i !== index)
    }));
  };

  const updateHowWeDoItStep = (index: number, field: 'title' | 'description', value: string) => {
    setFormData(prev => ({
      ...prev,
      howWeDoIt: prev.howWeDoIt.map((step, i) =>
        i === index ? { ...step, [field]: value } : step
      )
    }));
  };

  const handleIconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validation
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        toast.error('Icon file must be less than 5MB');
        return;
      }

      // Updated to accept PNG/JPG instead of SVG
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Only PNG, JPG, WebP, and GIF files are allowed for icons');
        return;
      }

      setFormData(prev => ({ ...prev, icons: file }));
      const reader = new FileReader();
      reader.onload = () => {
        setIconPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validation
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        toast.error('Image must be less than 5MB');
        return;
      }

      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Only JPEG, PNG, and WebP images are allowed');
        return;
      }

      setFormData(prev => ({ ...prev, image: file }));
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const validatePhase1 = () => {
    if (!formData.title.trim()) {
      toast.error('Service title is required');
      return false;
    }
    
    if (!formData.description.trim()) {
      toast.error('Service description is required');
      return false;
    }

    if (formData.description.length > 1000) {
      toast.error('Description cannot exceed 1000 characters');
      return false;
    }

    if (formData.description2 && formData.description2.length > 1000) {
      toast.error('Secondary description cannot exceed 1000 characters');
      return false;
    }

    return true;
  };

  const validatePhase2 = () => {
    if (mode === 'create' && !formData.icons) {
      toast.error('SVG icon is required');
      return false;
    }
    
    if (mode === 'create' && !formData.image) {
      toast.error('Service image is required');
      return false;
    }

    // Validate howWeDoIt steps
    for (let i = 0; i < formData.howWeDoIt.length; i++) {
      const step = formData.howWeDoIt[i];
      if (step.title && step.title.length > 200) {
        toast.error(`Step ${i + 1} title cannot exceed 200 characters`);
        return false;
      }
      if (step.description && step.description.length > 2000) {
        toast.error(`Step ${i + 1} description cannot exceed 2000 characters`);
        return false;
      }
    }

    return true;
  };

  const handleNextPhase = () => {
    if (validatePhase1()) {
      setCurrentPhase(2);
    }
  };

  const handlePrevPhase = () => {
    setCurrentPhase(1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validatePhase1() || !validatePhase2()) {
      return;
    }

    try {
      setLoading(true);
      
      if (mode === 'create') {
        const createData: CreateServiceRequest = {
          title: formData.title,
          description: formData.description,
          description2: formData.description2 || undefined,
          howWeDoIt: formData.howWeDoIt.length > 0 ? formData.howWeDoIt : undefined,
          sortOrder: formData.sortOrder,
          icons: formData.icons!,
          image: formData.image!,
        };
        
        const response = await servicesApi.create(createData);
        if (response.success) {
          toast.success('Service created successfully');
          navigate('/services');
        } else {
          toast.error(response.message || 'Failed to create service');
        }
      } else {
        const updateData: UpdateServiceRequest = {
          title: formData.title,
          description: formData.description,
          description2: formData.description2 || undefined,
          howWeDoIt: formData.howWeDoIt.length > 0 ? formData.howWeDoIt : undefined,
          sortOrder: formData.sortOrder,
          icons: formData.icons || undefined,
          image: formData.image || undefined,
        };
        
        const response = await servicesApi.update(id!, updateData);
        if (response.success) {
          toast.success('Service updated successfully');
          navigate('/services');
        } else {
          toast.error(response.message || 'Failed to update service');
        }
      }
    } catch (error) {
      console.error('Error saving service:', error);
      toast.error('Failed to save service');
    } finally {
      setLoading(false);
    }
  };

  if (mode === 'edit' && loading && !service) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/services')}
          className="text-gray-500 hover:text-gray-700 flex items-center gap-2"
        >
          <ArrowLeft size={20} />
          Back to Services
        </button>
        <h1 className="text-3xl font-bold text-gray-900">
          {mode === 'create' ? 'Create New Service' : 'Edit Service'}
        </h1>
      </div>

      {/* Phase Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-center">
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentPhase >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
            }`}>
              1
            </div>
            <span className={`ml-2 text-sm font-medium ${
              currentPhase >= 1 ? 'text-blue-600' : 'text-gray-500'
            }`}>
              Basic Information
            </span>
          </div>
          
          <div className={`w-16 h-1 mx-4 ${
            currentPhase >= 2 ? 'bg-blue-600' : 'bg-gray-300'
          }`}></div>
          
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentPhase >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
            }`}>
              2
            </div>
            <span className={`ml-2 text-sm font-medium ${
              currentPhase >= 2 ? 'text-blue-600' : 'text-gray-500'
            }`}>
              Files & Details
            </span>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          {/* Phase 1: Basic Information */}
          {currentPhase === 1 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
              
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter service title"
                  maxLength={200}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">{formData.title.length}/200 characters</p>
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Description *
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter primary service description"
                  maxLength={1000}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">{formData.description.length}/1000 characters</p>
              </div>

              <div>
                <label htmlFor="description2" className="block text-sm font-medium text-gray-700 mb-2">
                  Secondary Description (Optional)
                </label>
                <textarea
                  id="description2"
                  name="description2"
                  value={formData.description2}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter secondary description"
                  maxLength={1000}
                />
                <p className="text-xs text-gray-500 mt-1">{formData.description2.length}/1000 characters</p>
              </div>

              <div>
                <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-2">
                  Sort Order
                </label>
                <input
                  type="number"
                  id="sortOrder"
                  name="sortOrder"
                  value={formData.sortOrder}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  placeholder="0"
                />
              </div>

              {/* Navigation */}
              <div className="flex justify-end pt-6 border-t">
                <button
                  type="button"
                  onClick={handleNextPhase}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center gap-2"
                >
                  Next: Files & Details
                  <ArrowRight size={16} />
                </button>
              </div>
            </div>
          )}

          {/* Phase 2: Files & Details */}
          {currentPhase === 2 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Files & Additional Details</h2>

              {/* Service Icon Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Icon * (PNG/JPG - Max 5MB)
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                  {iconPreview ? (
                    <div className="relative">
                      <div className="flex items-center justify-center">
                        <img
                          src={iconPreview}
                          alt="Icon Preview"
                          className="w-16 h-16 object-contain"
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          setIconPreview('');
                          setFormData(prev => ({ ...prev, icons: null }));
                        }}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-4">
                        <label htmlFor="icons" className="cursor-pointer">
                          <span className="mt-2 block text-sm font-medium text-gray-900">
                            Click to upload service icon (PNG/JPG)
                          </span>
                          <input
                            id="icons"
                            type="file"
                            accept=".png,.jpg,.jpeg,.webp,.gif,image/png,image/jpeg,image/webp,image/gif"
                            onChange={handleIconChange}
                            className="hidden"
                            required={mode === 'create'}
                          />
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Service Image Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Image * (Max 5MB)
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                  {imagePreview ? (
                    <div className="relative">
                      <img
                        src={imagePreview}
                        alt="Service Preview"
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setImagePreview('');
                          setFormData(prev => ({ ...prev, image: null }));
                        }}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-4">
                        <label htmlFor="image" className="cursor-pointer">
                          <span className="mt-2 block text-sm font-medium text-gray-900">
                            Click to upload service image
                          </span>
                          <input
                            id="image"
                            type="file"
                            accept="image/*"
                            onChange={handleImageChange}
                            className="hidden"
                            required={mode === 'create'}
                          />
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* How We Do It Section */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">How We Do It Steps (Optional)</h3>
                  <button
                    type="button"
                    onClick={addHowWeDoItStep}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm flex items-center gap-1"
                  >
                    <Plus size={14} />
                    Add Step
                  </button>
                </div>

                {formData.howWeDoIt.length === 0 ? (
                  <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <p className="text-gray-500 mb-3">No steps added yet</p>
                    <button
                      type="button"
                      onClick={addHowWeDoItStep}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm flex items-center gap-2 mx-auto"
                    >
                      <Plus size={16} />
                      Add First Step
                    </button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {formData.howWeDoIt.map((step, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900">Step {index + 1}</h4>
                          <button
                            type="button"
                            onClick={() => removeHowWeDoItStep(index)}
                            className="text-red-500 hover:text-red-700"
                            title="Remove Step"
                          >
                            <X size={16} />
                          </button>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Step Title
                            </label>
                            <input
                              type="text"
                              value={step.title || ''}
                              onChange={(e) => updateHowWeDoItStep(index, 'title', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder={`e.g., Step ${index + 1}: Planning`}
                              maxLength={200}
                            />
                            <p className="text-xs text-gray-500 mt-1">{(step.title || '').length}/200 characters</p>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Step Description
                            </label>
                            <textarea
                              value={step.description || ''}
                              onChange={(e) => updateHowWeDoItStep(index, 'description', e.target.value)}
                              rows={3}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Describe this step in detail..."
                              maxLength={2000}
                            />
                            <p className="text-xs text-gray-500 mt-1">{(step.description || '').length}/2000 characters</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Navigation */}
              <div className="flex justify-between pt-6 border-t">
                <button
                  type="button"
                  onClick={handlePrevPhase}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center gap-2"
                >
                  <BackIcon size={16} />
                  Back: Basic Info
                </button>

                <div className="flex gap-4">
                  <button
                    type="button"
                    onClick={() => navigate('/services')}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {loading ? 'Saving...' : mode === 'create' ? 'Create Service' : 'Update Service'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </form>
    </div>
  );
};

export default ServiceForm;
