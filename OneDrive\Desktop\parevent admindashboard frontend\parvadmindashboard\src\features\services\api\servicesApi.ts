import axios from 'axios';
import { CreateServiceRequest, UpdateServiceRequest, ServiceFilters, ServiceStats } from '../types/service';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8005';

const servicesEndpoints = {
  getAll: `${API_BASE_URL}/api/services`,
  getById: (id: string) => `${API_BASE_URL}/api/services/${id}`,
  create: `${API_BASE_URL}/api/services`,
  update: (id: string) => `${API_BASE_URL}/api/services/${id}`,
  delete: (id: string) => `${API_BASE_URL}/api/services/${id}`,
  statistics: `${API_BASE_URL}/api/services/statistics`,
};

export const servicesApi = {
  // Get all services
  getAll: async (filters?: ServiceFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);
      if (filters?.search) params.append('search', filters.search);

      const response = await axios.get(`${servicesEndpoints.getAll}?${params.toString()}`);
      console.log('Services API Raw Response:', response.data);
      if (response.data.success && response.data.data && response.data.data.services) {
        console.log('First service from API:', response.data.data.services[0]);
        console.log('Icon field in first service:', response.data.data.services[0]?.icons);
        console.log('All fields in first service:', Object.keys(response.data.data.services[0] || {}));
      }
      return response.data;
    } catch (error: any) {
      console.error('Error fetching services:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch services',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Get service by ID
  getById: async (id: string) => {
    try {
      const response = await axios.get(servicesEndpoints.getById(id));
      return response.data;
    } catch (error: any) {
      console.error('Error fetching service:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch service',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Create service
  create: async (data: CreateServiceRequest) => {
    try {
      const formData = new FormData();

      formData.append('title', data.title);
      formData.append('description', data.description);
      if (data.description2) formData.append('description2', data.description2);

      // Handle howWeDoIt array
      if (data.howWeDoIt && data.howWeDoIt.length > 0) {
        data.howWeDoIt.forEach((step, index) => {
          if (step.title) formData.append(`howWeDoIt[${index}][title]`, step.title);
          if (step.description) formData.append(`howWeDoIt[${index}][description]`, step.description);
        });
      }

      if (data.sortOrder !== undefined) formData.append('sortOrder', data.sortOrder.toString());
      formData.append('icons', data.icons);
      formData.append('image', data.image);

      const response = await axios.post(servicesEndpoints.create, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Error creating service:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to create service',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Update service
  update: async (id: string, data: UpdateServiceRequest) => {
    try {
      const formData = new FormData();
      
      if (data.title) formData.append('title', data.title);
      if (data.description) formData.append('description', data.description);
      if (data.description2) formData.append('description2', data.description2);

      // Handle howWeDoIt array for update
      if (data.howWeDoIt && data.howWeDoIt.length > 0) {
        data.howWeDoIt.forEach((step, index) => {
          if (step.title) formData.append(`howWeDoIt[${index}][title]`, step.title);
          if (step.description) formData.append(`howWeDoIt[${index}][description]`, step.description);
        });
      }

      if (data.sortOrder !== undefined) formData.append('sortOrder', data.sortOrder.toString());
      if (data.icons) formData.append('icons', data.icons);
      if (data.image) formData.append('image', data.image);

      const response = await axios.put(servicesEndpoints.update(id), formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Error updating service:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to update service',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Delete service
  delete: async (id: string) => {
    try {
      const response = await axios.delete(servicesEndpoints.delete(id));
      return response.data;
    } catch (error: any) {
      console.error('Error deleting service:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to delete service',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Get statistics
  getStatistics: async (): Promise<{ success: boolean; data?: ServiceStats; message?: string }> => {
    try {
      const response = await axios.get(servicesEndpoints.statistics);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching service statistics:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch statistics',
      };
    }
  },
};
