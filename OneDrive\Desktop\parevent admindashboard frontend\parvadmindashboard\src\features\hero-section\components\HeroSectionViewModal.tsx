import React from 'react';
import { X, Crown, ExternalLink, Clock, Calendar } from 'lucide-react';
import { HeroSection } from '../types/heroSection';

interface HeroSectionViewModalProps {
  heroSection: HeroSection;
  isOpen: boolean;
  onClose: () => void;
}

const HeroSectionViewModal: React.FC<HeroSectionViewModalProps> = ({ heroSection, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <h2 className="text-2xl font-bold text-gray-900">Hero Section Details</h2>
            {heroSection.isPrimary && (
              <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-medium">
                <Crown size={14} className="fill-current" />
                Primary
              </div>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Hero Images Carousel */}
          <div className="mb-6">
            {heroSection.images && heroSection.images.length > 0 ? (
              <div className="space-y-4">
                {/* Active Image Display */}
                <div className="relative rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={heroSection.images[heroSection.activeImageIndex || 0] || heroSection.images[0]}
                    alt={heroSection.title}
                    className="w-full h-64 object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                    <div className="text-center text-white p-6">
                      <h1 className="text-4xl font-bold mb-2">{heroSection.title}</h1>
                      {heroSection.subtitle && (
                        <h2 className="text-xl mb-4">{heroSection.subtitle}</h2>
                      )}
                      {heroSection.description && (
                        <p className="text-lg mb-6 max-w-2xl">{heroSection.description}</p>
                      )}
                      {heroSection.buttonText && (
                        <div className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium">
                          {heroSection.buttonText}
                          <ExternalLink size={16} />
                        </div>
                      )}
                    </div>
                  </div>
                  {/* Active Image Badge */}
                  <div className="absolute top-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Active: Image {(heroSection.activeImageIndex || 0) + 1}
                  </div>
                </div>

                {/* All Images Thumbnails */}
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">All Images ({heroSection.images.length}/3)</h4>
                  <div className="grid grid-cols-3 gap-4">
                    {heroSection.images.slice(0, 3).map((imageUrl, index) => (
                      <div key={index} className="relative">
                        <img
                          src={imageUrl}
                          alt={`${heroSection.title} - Image ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        {/* Active Indicator */}
                        {index === (heroSection.activeImageIndex || 0) && (
                          <div className="absolute inset-0 bg-blue-600 bg-opacity-20 rounded-lg border-2 border-blue-600 flex items-center justify-center">
                            <div className="bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                              LIVE
                            </div>
                          </div>
                        )}
                        <div className="absolute bottom-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                          Image {index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="relative rounded-lg overflow-hidden bg-gray-100">
                <img
                  src={heroSection.imageUrls?.[0] || heroSection.image}
                  alt={heroSection.title}
                  className="w-full h-64 object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                  <div className="text-center text-white p-6">
                    <h1 className="text-4xl font-bold mb-2">{heroSection.title}</h1>
                    {heroSection.subtitle && (
                      <h2 className="text-xl mb-4">{heroSection.subtitle}</h2>
                    )}
                    {heroSection.description && (
                      <p className="text-lg mb-6 max-w-2xl">{heroSection.description}</p>
                    )}
                    {heroSection.buttonText && (
                      <div className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium">
                        {heroSection.buttonText}
                        <ExternalLink size={16} />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Hero Section Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Basic Information</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Title</label>
                    <p className="text-gray-900 font-medium">{heroSection.title}</p>
                  </div>
                  
                  {heroSection.subtitle && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Subtitle</label>
                      <p className="text-gray-900">{heroSection.subtitle}</p>
                    </div>
                  )}
                  
                  {heroSection.description && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Description</label>
                      <p className="text-gray-900">{heroSection.description}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Button Information */}
              {(heroSection.buttonText || heroSection.buttonLink) && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">Button Configuration</h4>
                  <div className="space-y-3">
                    {heroSection.buttonText && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Button Text</label>
                        <p className="text-gray-900">{heroSection.buttonText}</p>
                      </div>
                    )}
                    
                    {heroSection.buttonLink && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Button Link</label>
                        <a 
                          href={heroSection.buttonLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 flex items-center gap-1"
                        >
                          {heroSection.buttonLink}
                          <ExternalLink size={14} />
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Settings & Status */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Settings & Status</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Primary Hero Section:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      heroSection.isPrimary 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {heroSection.isPrimary ? 'Yes' : 'No'}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Display Duration:</span>
                    <div className="flex items-center gap-1">
                      <Clock size={14} className="text-gray-500" />
                      <span className="font-medium">{heroSection.displayDuration}ms</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Timestamps</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Created:</span>
                    <div className="flex items-center gap-1">
                      <Calendar size={14} className="text-gray-500" />
                      <span className="font-medium">
                        {new Date(heroSection.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Updated:</span>
                    <div className="flex items-center gap-1">
                      <Calendar size={14} className="text-gray-500" />
                      <span className="font-medium">
                        {new Date(heroSection.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Image Information */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Image Information</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="text-gray-600">Image URL:</span>
                    <p className="text-xs text-gray-500 break-all mt-1">
                      {heroSection.imageUrls?.[0] || heroSection.image}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default HeroSectionViewModal;
