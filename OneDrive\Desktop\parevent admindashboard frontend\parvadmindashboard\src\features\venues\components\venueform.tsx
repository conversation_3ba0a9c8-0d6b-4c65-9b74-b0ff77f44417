import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ArrowLeft, Upload, X } from 'lucide-react';
import {
  Venue,
  CreateVenueRequest,
  UpdateVenueRequest,
  VENUE_TYPES
} from '../types/venue';
import { venuesApi } from '../api/venuesApi';

interface VenueFormProps {
  mode: 'create' | 'edit';
}

const VenueForm: React.FC<VenueFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [venue, setVenue] = useState<Venue | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');


  const [formData, setFormData] = useState({
    name: '',
    venueType: 'banquet-hall' as const,
    location: '',
    capacity: 0,
    seats: 0,
    sortOrder: 1,
    image: null as File | null,
  });

  useEffect(() => {
    if (mode === 'edit' && id) {
      fetchVenue();
    }
  }, [mode, id]);

  const fetchVenue = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const response = await venuesApi.getById(id);
      if (response.success) {
        const venueData = response.data;
        setVenue(venueData);
        setFormData({
          name: venueData.name,
          venueType: venueData.venueType,
          location: venueData.location,
          capacity: venueData.capacity,
          seats: venueData.seats,
          sortOrder: venueData.sortOrder,
          image: null,
        });
        setImagePreview(venueData.imageUrl || venueData.image);
      } else {
        toast.error('Venue not found');
        navigate('/venues');
      }
    } catch (error) {
      console.error('Error fetching venue:', error);
      toast.error('Failed to fetch venue data');
      navigate('/venues');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (name.includes('.')) {
      const [parent, child, grandchild] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: grandchild ? {
            ...(prev[parent as keyof typeof prev] as any)[child],
            [grandchild]: type === 'number' ? parseFloat(value) || 0 : value
          } : type === 'number' ? parseFloat(value) || 0 : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'number' ? parseFloat(value) || 0 : 
                type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
      }));
    }
  };

  const handleAmenityChange = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const handleAvailabilityChange = (day: keyof VenueAvailability) => {
    setFormData(prev => ({
      ...prev,
      availability: {
        ...prev.availability,
        [day]: !prev.availability[day]
      }
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validation
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        toast.error('Image must be less than 5MB');
        return;
      }

      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Only JPEG, PNG, and WebP images are allowed');
        return;
      }

      setFormData(prev => ({ ...prev, image: file }));
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.name.trim()) {
      toast.error('Venue name is required');
      return;
    }

    if (!formData.location.trim()) {
      toast.error('Location is required');
      return;
    }

    if (formData.capacity < 1) {
      toast.error('Capacity must be at least 1');
      return;
    }

    if (formData.seats < 1) {
      toast.error('Seats must be at least 1');
      return;
    }
    
    if (mode === 'create' && !formData.image) {
      toast.error('Main image is required');
      return;
    }

    try {
      setLoading(true);
      
      if (mode === 'create') {
        const createData: CreateVenueRequest = {
          name: formData.name,
          venueType: formData.venueType,
          location: formData.location,
          capacity: formData.capacity,
          seats: formData.seats,
          sortOrder: formData.sortOrder,
          image: formData.image!,
        };
        
        const response = await venuesApi.create(createData);
        if (response.success) {
          toast.success('Venue created successfully');
          navigate('/venues');
        } else {
          toast.error(response.message || 'Failed to create venue');
        }
      } else {
        const updateData: UpdateVenueRequest = {
          name: formData.name,
          venueType: formData.venueType,
          location: formData.location,
          capacity: formData.capacity,
          seats: formData.seats,
          sortOrder: formData.sortOrder,
          image: formData.image || undefined,
        };
        
        const response = await venuesApi.update(id!, updateData);
        if (response.success) {
          toast.success('Venue updated successfully');
          navigate('/venues');
        } else {
          toast.error(response.message || 'Failed to update venue');
        }
      }
    } catch (error) {
      console.error('Error saving venue:', error);
      toast.error('Failed to save venue');
    } finally {
      setLoading(false);
    }
  };

  if (mode === 'edit' && loading && !venue) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/venues')}
          className="text-gray-500 hover:text-gray-700 flex items-center gap-2"
        >
          <ArrowLeft size={20} />
          Back to Venues
        </button>
        <h1 className="text-3xl font-bold text-gray-900">
          {mode === 'create' ? 'Create New Venue' : 'Edit Venue'}
        </h1>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="max-w-4xl">
        <div className="bg-white rounded-lg shadow-lg p-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Venue Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Venue Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter venue name"
                  required
                />
              </div>

              <div>
                <label htmlFor="venueType" className="block text-sm font-medium text-gray-700 mb-2">
                  Venue Type *
                </label>
                <select
                  id="venueType"
                  name="venueType"
                  value={formData.venueType}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  {VENUE_TYPES.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                  Location *
                </label>
                <textarea
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter complete venue location (e.g., 123 Main Street, Mumbai, Maharashtra)"
                  required
                />
              </div>
            </div>
          </div>

          {/* Capacity Information */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Capacity Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="capacity" className="block text-sm font-medium text-gray-700 mb-2">
                  Total Capacity *
                </label>
                <input
                  type="number"
                  id="capacity"
                  name="capacity"
                  value={formData.capacity}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter total capacity"
                  min="1"
                  required
                />
              </div>

              <div>
                <label htmlFor="seats" className="block text-sm font-medium text-gray-700 mb-2">
                  Seating Capacity *
                </label>
                <input
                  type="number"
                  id="seats"
                  name="seats"
                  value={formData.seats}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter seating capacity"
                  min="1"
                  required
                />
              </div>

              <div>
                <label htmlFor="sortOrder" className="block text-sm font-medium text-gray-700 mb-2">
                  Sort Order
                </label>
                <input
                  type="number"
                  id="sortOrder"
                  name="sortOrder"
                  value={formData.sortOrder}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="1"
                  min="0"
                />
              </div>
            </div>
          </div>

          {/* Image Upload */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Venue Image</h2>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              {imagePreview ? (
                <div className="relative">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      setImagePreview('');
                      setFormData(prev => ({ ...prev, image: null }));
                    }}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <X size={16} />
                  </button>
                </div>
              ) : (
                <div className="text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <label htmlFor="image" className="cursor-pointer">
                      <span className="mt-2 block text-sm font-medium text-gray-900">
                        Click to upload venue image
                      </span>
                      <input
                        id="image"
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className="hidden"
                        required={mode === 'create'}
                      />
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4 pt-6 border-t">
            <button
              type="button"
              onClick={() => navigate('/venues')}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? 'Saving...' : mode === 'create' ? 'Create Venue' : 'Update Venue'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default VenueForm;
