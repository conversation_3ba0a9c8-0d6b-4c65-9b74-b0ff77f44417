import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

interface VenueFormProps {
  mode: 'create' | 'edit';
}

const VenueForm: React.FC<VenueFormProps> = ({ mode }) => {
  const navigate = useNavigate();

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <button
          onClick={() => navigate('/venues')}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-4"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Venues
        </button>
        <h1 className="text-3xl font-bold text-gray-900">
          {mode === 'create' ? 'Create New Venue' : 'Edit Venue'}
        </h1>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <p className="text-gray-600">
          Venue form component - Coming soon
        </p>
      </div>
    </div>
  );
};

export default VenueForm;
