export interface Review {
  _id: string;
  name: string;
  relationship: 'bride' | 'groom' | 'parent' | 'friend' | 'relative' | 'colleague' | 'client' | 'other';
  review: string;
  star: number;
  eventType: 'wedding' | 'corporate' | 'birthday' | 'anniversary' | 'other';
  isFeatured: boolean;
  image?: string;
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateReviewRequest {
  name: string;
  relationship: 'bride' | 'groom' | 'parent' | 'friend' | 'relative' | 'colleague' | 'client' | 'other';
  review: string;
  star: number;
  eventType: 'wedding' | 'corporate' | 'birthday' | 'anniversary' | 'other';
  isFeatured: boolean;
  image?: File;
}

export interface UpdateReviewRequest {
  name?: string;
  relationship?: 'bride' | 'groom' | 'parent' | 'friend' | 'relative' | 'colleague' | 'client' | 'other';
  review?: string;
  star?: number;
  eventType?: 'wedding' | 'corporate' | 'birthday' | 'anniversary' | 'other';
  isFeatured?: boolean;
  image?: File;
}

export interface ReviewFilters {
  relationship?: string;
  eventType?: string;
  star?: number;
  isFeatured?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  featuredCount: number;
}

export const RELATIONSHIP_TYPES = [
  { value: 'bride', label: 'Bride' },
  { value: 'groom', label: 'Groom' },
  { value: 'parent', label: 'Parent' },
  { value: 'friend', label: 'Friend' },
  { value: 'relative', label: 'Relative' },
  { value: 'colleague', label: 'Colleague' },
  { value: 'client', label: 'Client' },
  { value: 'other', label: 'Other' },
] as const;

export const EVENT_TYPES = [
  { value: 'wedding', label: 'Wedding' },
  { value: 'corporate', label: 'Corporate' },
  { value: 'birthday', label: 'Birthday' },
  { value: 'anniversary', label: 'Anniversary' },
  { value: 'other', label: 'Other' },
] as const;

// Keep the old exports for backward compatibility
export const RELATIONSHIP_OPTIONS = RELATIONSHIP_TYPES;
export const EVENT_TYPE_OPTIONS = EVENT_TYPES;
