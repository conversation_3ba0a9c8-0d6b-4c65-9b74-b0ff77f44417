// Venue Booking types based on simplified API documentation

export interface VenueBooking {
  _id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  venueId: string;
  venueName: string;
  dateOfPlan: string;
  message: string;
  status: 'new' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateVenueBookingRequest {
  fullName: string;
  email: string;
  phoneNumber: string;
  venueId: string;
  venueName: string;
  dateOfPlan: string;
  message: string;
}

export interface UpdateVenueBookingRequest {
  fullName?: string;
  email?: string;
  phoneNumber?: string;
  venueId?: string;
  venueName?: string;
  dateOfPlan?: string;
  message?: string;
  status?: 'new' | 'in-progress' | 'resolved' | 'closed';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export interface VenueBookingFilters {
  status?: string;
  priority?: string;
  venueId?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export const BOOKING_STATUSES = [
  { value: 'new', label: 'New', color: 'blue' },
  { value: 'in-progress', label: 'In Progress', color: 'yellow' },
  { value: 'resolved', label: 'Resolved', color: 'green' },
  { value: 'closed', label: 'Closed', color: 'gray' },
] as const;

export const BOOKING_PRIORITIES = [
  { value: 'low', label: 'Low', color: 'gray' },
  { value: 'medium', label: 'Medium', color: 'blue' },
  { value: 'high', label: 'High', color: 'orange' },
  { value: 'urgent', label: 'Urgent', color: 'red' },
] as const;


