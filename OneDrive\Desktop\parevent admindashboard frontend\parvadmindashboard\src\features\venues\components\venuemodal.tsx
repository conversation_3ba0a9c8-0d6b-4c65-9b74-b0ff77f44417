import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'react-toastify';
import { X, Upload } from 'lucide-react';
import { CreateVenueRequest, VENUE_TYPES, VenueType } from '../types/venue';
import { venuesApi } from '../api/venuesApi';

type VenueFormData = {
  name: string;
  venueType: VenueType;
  location: string;
  capacity: number;
  seats: number;
  sortOrder: number;
  image: FileList;
};

type CreateVenueModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
};

const CreateVenueModal: React.FC<CreateVenueModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const { register, handleSubmit, reset, formState: { errors } } = useForm<VenueFormData>({
    defaultValues: {
      sortOrder: 1,
      venueType: 'banquet-hall'
    }
  });

  const onFormSubmit = async (data: VenueFormData) => {
    if (!data.image || data.image.length === 0) {
      toast.error('Please select an image');
      return;
    }

    try {
      setLoading(true);

      const createData: CreateVenueRequest = {
        name: data.name.trim(),
        venueType: data.venueType,
        location: data.location.trim(),
        capacity: Number(data.capacity),
        seats: Number(data.seats),
        sortOrder: Number(data.sortOrder),
        image: data.image[0],
      };

      const response = await venuesApi.create(createData);

      if (response.success) {
        toast.success('Venue created successfully!');
        reset();
        setPreviewUrl(null);
        onClose();
        onSuccess();
      } else {
        toast.error(response.message || 'Failed to create venue');
      }
    } catch (error) {
      console.error('Error creating venue:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        toast.error('Image must be less than 5MB');
        return;
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Only JPEG, PNG, and WebP images are allowed');
        return;
      }

      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const clearFile = () => {
    const fileInput = document.getElementById('venue-image') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
    setPreviewUrl(null); // Clear preview
  };

  const handleClose = () => {
    reset();
    setPreviewUrl(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-[#F89453] text-lg font-bold">Create New Venue</DialogTitle>
        </DialogHeader>
        <div className="w-full bg-white p-3">
          <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Venue Image *
              </label>
              {previewUrl ? (
                <div className="relative inline-block">
                  <img
                    src={previewUrl}
                    alt="Venue preview"
                    className="w-48 h-32 object-cover rounded-lg border"
                  />
                  <button
                    type="button"
                    onClick={clearFile}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-600">Upload venue image (required)</p>
                </div>
              )}
              <input
                id="venue-image"
                type="file"
                accept="image/*"
                {...register('image', {
                  required: 'Image is required',
                  onChange: handleFileChange
                })}
                className="mt-2 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              {errors.image && (
                <p className="mt-1 text-sm text-red-600">{errors.image.message}</p>
              )}
            </div>
            <div className='w-full flex flex-row justify-center items-center gap-5'>
                <div className='w-1/2'>
              <label className="block text-sm font-medium text-gray-700">Name</label>
              <input
                type="text"
                {...register('name', { required: true })}
                className="mt-1 block w-full p-2 border outline-none  border-[#313131] rounded-md "
                placeholder="Enter venue name"
              />
            </div>
            <div className='w-1/2'>
              <label className="block text-sm font-medium text-gray-700">Location</label>
              <input
                type="text"
                {...register('location', { required: true })}
                className="mt-1 block w-full p-2 border border-[#313131] outline-none  rounded-md "
                placeholder="Enter location"
              />
            </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Category (Type of Venue)</label>
              <select
                {...register('category', { required: true })}
                className="mt-1 block w-full p-2 border border-[#313131] outline-none rounded-md "
              >
                <option value="">Select a category</option>
                <option value="Conference">Conference</option>
                <option value="Wedding">Wedding</option>
                <option value="Concert">Concert</option>
                <option value="Party">Party</option>
              </select>
            </div>

            <div className='w-full flex flex-row justify-center items-center gap-5'>
            <div className='w-1/2'>
              <label className="block text-sm font-medium text-gray-700">Total Capacity</label>
              <input
                type="number"
                {...register('totalCapacity', { required: true, min: 1 })}
                className="mt-1 block w-full p-2 border border-[#313131] rounded-md outline-none"
                placeholder="Enter total capacity"
                min="1"
              />
            </div>
            <div className='w-1/2'>
              <label className="block text-sm font-medium text-gray-700">Total Seats</label>
              <input
                type="number"
                {...register('totalSeats', { required: true, min: 0 })}
                className="mt-1 block w-full p-2 border border-[#313131] rounded-md outline-none"
                placeholder="Enter total seats"
                min="0"
              />
            </div>
            </div>
            <div className="flex items-center justify-center gap-3">
              <button
                type="submit"
                className="w-[10rem] bg-[#F89453] text-white p-2 rounded-md hover:bg-[#313131] transition duration-200"
              >
                Submit
              </button>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="w-[10rem] bg-gray-600 text-white p-2 rounded-md hover:bg-gray-700 transition duration-200"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VenueModal;