import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'react-toastify';
import { X, Upload } from 'lucide-react';
import { CreateVenueRequest, VENUE_TYPES, VenueType } from '../types/venue';
import { venuesApi } from '../api/venuesApi';

type VenueFormData = {
  name: string;
  venueType: VenueType;
  location: string;
  capacity: number;
  seats: number;
  sortOrder: number;
  image: FileList;
};

type CreateVenueModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
};

const CreateVenueModal: React.FC<CreateVenueModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const { register, handleSubmit, reset, formState: { errors } } = useForm<VenueFormData>({
    defaultValues: {
      sortOrder: 1,
      venueType: 'banquet-hall'
    }
  });

  const onFormSubmit = async (data: VenueFormData) => {
    if (!data.image || data.image.length === 0) {
      toast.error('Please select an image');
      return;
    }

    try {
      setLoading(true);

      const createData: CreateVenueRequest = {
        name: data.name.trim(),
        venueType: data.venueType,
        location: data.location.trim(),
        capacity: Number(data.capacity),
        seats: Number(data.seats),
        sortOrder: Number(data.sortOrder),
        image: data.image[0],
      };

      const response = await venuesApi.create(createData);

      if (response.success) {
        toast.success('Venue created successfully!');
        reset();
        setPreviewUrl(null);
        onClose();
        onSuccess();
      } else {
        toast.error(response.message || 'Failed to create venue');
      }
    } catch (error) {
      console.error('Error creating venue:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        toast.error('Image must be less than 5MB');
        return;
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Only JPEG, PNG, and WebP images are allowed');
        return;
      }

      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const clearFile = () => {
    const fileInput = document.getElementById('venue-image') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
    setPreviewUrl(null); // Clear preview
  };

  const handleClose = () => {
    reset();
    setPreviewUrl(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-[#F89453] text-lg font-bold">Create New Venue</DialogTitle>
        </DialogHeader>
        <div className="w-full bg-white p-3">
          <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Venue Image *
              </label>
              {previewUrl ? (
                <div className="relative inline-block">
                  <img
                    src={previewUrl}
                    alt="Venue preview"
                    className="w-48 h-32 object-cover rounded-lg border"
                  />
                  <button
                    type="button"
                    onClick={clearFile}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-600">Upload venue image (required)</p>
                </div>
              )}
              <input
                id="venue-image"
                type="file"
                accept="image/*"
                {...register('image', {
                  required: 'Image is required',
                  onChange: handleFileChange
                })}
                className="mt-2 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              {errors.image && (
                <p className="mt-1 text-sm text-red-600">{errors.image.message}</p>
              )}
            </div>
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Venue Name *
                </label>
                <input
                  type="text"
                  {...register('name', {
                    required: 'Venue name is required',
                    maxLength: { value: 200, message: 'Name must be less than 200 characters' }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter venue name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Venue Type *
                </label>
                <select
                  {...register('venueType', { required: 'Venue type is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {VENUE_TYPES.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
                {errors.venueType && (
                  <p className="mt-1 text-sm text-red-600">{errors.venueType.message}</p>
                )}
              </div>
            </div>

            {/* Location */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Location *
              </label>
              <input
                type="text"
                {...register('location', {
                  required: 'Location is required',
                  maxLength: { value: 500, message: 'Location must be less than 500 characters' }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter venue location"
              />
              {errors.location && (
                <p className="mt-1 text-sm text-red-600">{errors.location.message}</p>
              )}
            </div>

            {/* Capacity and Seats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Capacity *
                </label>
                <input
                  type="number"
                  {...register('capacity', {
                    required: 'Capacity is required',
                    min: { value: 1, message: 'Capacity must be at least 1' },
                    max: { value: 15000, message: 'Capacity cannot exceed 15000' }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter capacity"
                  min="1"
                  max="15000"
                />
                {errors.capacity && (
                  <p className="mt-1 text-sm text-red-600">{errors.capacity.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Seats *
                </label>
                <input
                  type="number"
                  {...register('seats', {
                    required: 'Seats is required',
                    min: { value: 1, message: 'Seats must be at least 1' },
                    max: { value: 10000, message: 'Seats cannot exceed 10000' }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter seats"
                  min="1"
                  max="10000"
                />
                {errors.seats && (
                  <p className="mt-1 text-sm text-red-600">{errors.seats.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sort Order
                </label>
                <input
                  type="number"
                  {...register('sortOrder', {
                    min: { value: 0, message: 'Sort order must be 0 or greater' }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter sort order"
                  min="0"
                />
                {errors.sortOrder && (
                  <p className="mt-1 text-sm text-red-600">{errors.sortOrder.message}</p>
                )}
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Venue'}
              </button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateVenueModal;