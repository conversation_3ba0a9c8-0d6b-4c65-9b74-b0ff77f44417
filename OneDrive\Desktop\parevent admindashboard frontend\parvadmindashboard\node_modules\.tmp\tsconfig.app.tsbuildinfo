{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/errorboundary.tsx", "../../src/components/svgicon.tsx", "../../src/components/pagination.tsx", "../../src/components/auth/errorboundary.tsx", "../../src/components/auth/loadingspinner.tsx", "../../src/components/auth/privateroute.tsx", "../../src/components/auth/publicroute.tsx", "../../src/components/auth/index.ts", "../../src/components/datatable/data-table.tsx", "../../src/components/errorpage/error-page.tsx", "../../src/components/globalfiles/debounce.tsx", "../../src/components/globalfiles/loader.tsx", "../../src/components/globalfiles/usepagination.tsx", "../../src/components/profile/profilemodal.tsx", "../../src/components/ui/accordion.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/chart.tsx", "../../src/components/ui/checkbox.tsx", "../../src/components/ui/collapsible.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/form.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/sheet.tsx", "../../src/components/ui/sidebar.tsx", "../../src/components/ui/skeleton.tsx", "../../src/components/ui/switch.tsx", "../../src/components/ui/table.tsx", "../../src/components/ui/textarea.tsx", "../../src/components/ui/tooltip.tsx", "../../src/features/raviewrate/index.tsx", "../../src/features/raviewrate/components/ratereviewmain.tsx", "../../src/features/raviewrate/components/reviewcolumntable.tsx", "../../src/features/raviewrate/components/reviewmodalfrom.tsx", "../../src/features/auth/components/loginform.tsx", "../../src/features/auth/pages/loginpage.tsx", "../../src/features/blog/components/blogcomments.tsx", "../../src/features/blog/components/publicblogcomments.tsx", "../../src/features/blog/components/tiptapeditor.tsx", "../../src/features/blog/components/api/api.tsx", "../../src/features/blog/components/table/add-blog.tsx", "../../src/features/blog/components/table/blog-actions.tsx", "../../src/features/blog/components/table/column.tsx", "../../src/features/blog/components/table/data-table.tsx", "../../src/features/blog/components/table/delete-blog.tsx", "../../src/features/blog/components/table/edit-blog.tsx", "../../src/features/blog/components/table/pagination.tsx", "../../src/features/blog/components/table/updateblog.tsx", "../../src/features/blog/pages/addblogpage.tsx", "../../src/features/blog/pages/editblogpage.tsx", "../../src/features/blog/pages/viewblogpage.tsx", "../../src/features/blog/routes/blog.tsx", "../../src/features/blog/routes/index.tsx", "../../src/features/blog/routes/update-blog.tsx", "../../src/features/blog/type/blogtype.ts", "../../src/features/careers/api/careerapi.ts", "../../src/features/careers/components/modals/viewcareermodal.tsx", "../../src/features/careers/components/table/columns.tsx", "../../src/features/careers/pages/careermanagementpage.tsx", "../../src/features/careers/routes/index.tsx", "../../src/features/contact/components/api/api.tsx", "../../src/features/contact/components/table/column.tsx", "../../src/features/contact/components/table/data-table.tsx", "../../src/features/contact/components/table/delete-contact.tsx", "../../src/features/contact/routes/contact.tsx", "../../src/features/contact/routes/index.tsx", "../../src/features/contact/type/contacttype.ts", "../../src/features/dashboard/components/mostsubscribed.tsx", "../../src/features/dashboard/components/area-chart.tsx", "../../src/features/dashboard/components/bar-graph.tsx", "../../src/features/dashboard/components/card.tsx", "../../src/features/dashboard/components/chart.tsx", "../../src/features/dashboard/data/card-data.tsx", "../../src/features/dashboard/data/chart-data.tsx", "../../src/features/dashboard/routes/dashboard-page.tsx", "../../src/features/dashboard/routes/index.tsx", "../../src/features/faqs/api/faqapi.ts", "../../src/features/faqs/components/modals/addfaqmodal.tsx", "../../src/features/faqs/components/modals/deletefaqmodal.tsx", "../../src/features/faqs/components/modals/editfaqmodal.tsx", "../../src/features/faqs/components/modals/viewfaqmodal.tsx", "../../src/features/faqs/components/table/faqdatatable.tsx", "../../src/features/faqs/components/table/columns.tsx", "../../src/features/faqs/pages/faqmanagementpage.tsx", "../../src/features/faqs/routes/index.tsx", "../../src/features/gallery/api/galleryapi.ts", "../../src/features/gallery/components/gallerydetail.tsx", "../../src/features/gallery/components/galleryform.tsx", "../../src/features/gallery/components/gallerylist.tsx", "../../src/features/gallery/routes/index.tsx", "../../src/features/gallery/types/gallery.ts", "../../src/features/hero-section/api/herosectionapi.ts", "../../src/features/hero-section/components/herosectionform.tsx", "../../src/features/hero-section/components/herosectionlist.tsx", "../../src/features/hero-section/components/herosectionviewmodal.tsx", "../../src/features/hero-section/routes/index.tsx", "../../src/features/hero-section/types/herosection.ts", "../../src/features/homeherosection/index.tsx", "../../src/features/homeherosection/componenets/allimageherosection.tsx", "../../src/features/homeherosection/componenets/heroimageform.tsx", "../../src/features/homeherosection/componenets/homeherosectionmain.tsx", "../../src/features/jobprofiles/api/jobapi.ts", "../../src/features/jobprofiles/components/modals/addjobmodal.tsx", "../../src/features/jobprofiles/components/modals/deletejobmodal.tsx", "../../src/features/jobprofiles/components/modals/editjobmodal.tsx", "../../src/features/jobprofiles/components/modals/viewjobmodal.tsx", "../../src/features/jobprofiles/components/table/columns.tsx", "../../src/features/jobprofiles/pages/jobprofilemanagementpage.tsx", "../../src/features/jobprofiles/routes/index.tsx", "../../src/features/layout/dashboard-layout.tsx", "../../src/features/layout/top-navbar.tsx", "../../src/features/postcomments/api/commentapi.ts", "../../src/features/postcomments/pages/commentmanagementpage.tsx", "../../src/features/postcomments/routes/index.tsx", "../../src/features/projects/index.tsx", "../../src/features/projects/api/projectapi.ts", "../../src/features/projects/components/deleteconfirmmodal.tsx", "../../src/features/projects/components/projectdatatable.tsx", "../../src/features/projects/components/projectmodal.tsx", "../../src/features/projects/components/projecttiptapeditor.tsx", "../../src/features/projects/components/projectviewmodal.tsx", "../../src/features/projects/components/projectfoldermain.tsx", "../../src/features/projects/types/projecttypes.ts", "../../src/features/reviews/api/reviewsapi.ts", "../../src/features/reviews/components/reviewdetail.tsx", "../../src/features/reviews/components/reviewform.tsx", "../../src/features/reviews/components/reviewviewmodal.tsx", "../../src/features/reviews/components/reviewslist.tsx", "../../src/features/reviews/routes/index.tsx", "../../src/features/reviews/types/review.ts", "../../src/features/services/api/servicesapi.ts", "../../src/features/services/components/serviceform.tsx", "../../src/features/services/components/serviceviewmodal.tsx", "../../src/features/services/components/serviceslist.tsx", "../../src/features/services/routes/index.tsx", "../../src/features/services/types/service.ts", "../../src/features/servicesfolder/index.tsx", "../../src/features/servicesfolder/api/serviceapi.ts", "../../src/features/servicesfolder/components/deleteconfirmmodal.tsx", "../../src/features/servicesfolder/components/servicedatatable.tsx", "../../src/features/servicesfolder/components/servicemodal.tsx", "../../src/features/servicesfolder/components/servicetiptapeditor.tsx", "../../src/features/servicesfolder/components/serviceviewmodal.tsx", "../../src/features/servicesfolder/components/serivedatacolumn.tsx", "../../src/features/servicesfolder/components/servicecardmodal.tsx", "../../src/features/servicesfolder/components/servicefoldermain.tsx", "../../src/features/servicesfolder/types/servicetypes.ts", "../../src/features/teams/index.tsx", "../../src/features/teams/components/api/api.tsx", "../../src/features/teams/components/table/add-team.tsx", "../../src/features/teams/components/table/column.tsx", "../../src/features/teams/components/table/data-table.tsx", "../../src/features/teams/components/table/delete-team.tsx", "../../src/features/teams/components/table/edit-team.tsx", "../../src/features/teams/routes/team.tsx", "../../src/features/teams/type/teamtype.ts", "../../src/features/test/apitestpage.tsx", "../../src/features/user/managemonetization.tsx", "../../src/features/user/manageuser.tsx", "../../src/features/user/manageverfication.tsx", "../../src/features/user/customprofile.tsx", "../../src/features/user/routes/index.tsx", "../../src/features/userform/form.tsx", "../../src/features/userform/index.tsx", "../../src/features/userform/api/api.tsx", "../../src/features/userform/components/column.tsx", "../../src/features/userform/components/data-table.tsx", "../../src/features/userform/type/form.type.tsx", "../../src/features/venue-bookings/api/venuebookingsapi.ts", "../../src/features/venue-bookings/components/venuebookingslist.tsx", "../../src/features/venue-bookings/routes/index.tsx", "../../src/features/venue-bookings/types/venuebooking.ts", "../../src/features/venues/index.tsx", "../../src/features/venues/api/venuesapi.ts", "../../src/features/venues/components/venueviewmodal.tsx", "../../src/features/venues/components/venueslist.tsx", "../../src/features/venues/components/venueeditmodal.tsx", "../../src/features/venues/components/venuefile.tsx", "../../src/features/venues/components/venueform.tsx", "../../src/features/venues/components/venuemodal.tsx", "../../src/features/venues/components/venuetable.tsx", "../../src/features/venues/components/venuetablecolumn.tsx", "../../src/features/venues/routes/index.tsx", "../../src/features/venues/types/venue.ts", "../../src/globalurl/baseurl.tsx", "../../src/hooks/use-mobile.tsx", "../../src/hooks/useauth.ts", "../../src/lib/utils.ts", "../../src/providers/authprovider.tsx", "../../src/providers/queryprovider.tsx", "../../src/providers/reduxprovider.tsx", "../../src/routes/index.tsx", "../../src/routes/router.tsx", "../../src/store/hooks.ts", "../../src/store/store.ts", "../../src/store/slices/authslice.ts", "../../src/types/auth.ts", "../../src/types/careers.ts", "../../src/types/faqs.ts", "../../src/types/jobprofiles.ts", "../../src/types/postcomments.ts", "../../src/utils/auth.ts", "../../src/utils/axiosinterceptors.ts", "../../src/utils/datahelpers.ts", "../../src/utils/mockdata.ts"], "errors": true, "version": "5.6.3"}