import React from 'react';
import { Routes, Route } from 'react-router-dom';
import GalleryList from '../components/GalleryList';
import GalleryForm from '../components/GalleryForm';
import GalleryDetail from '../components/GalleryDetail';

const GalleryRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<GalleryList />} />
      <Route path="create" element={<GalleryForm mode="create" />} />
      <Route path="edit/:id" element={<GalleryForm mode="edit" />} />
      <Route path=":id" element={<GalleryDetail />} />
    </Routes>
  );
};

export default GalleryRoutes;
