import React, { useState, useEffect } from 'react';

interface SvgIconProps {
  src: string;
  alt?: string;
  className?: string;
  fallback?: React.ReactNode;
}

const SvgIcon: React.FC<SvgIconProps> = ({ 
  src, 
  alt = "Icon", 
  className = "w-6 h-6", 
  fallback = <div className={`${className} bg-gray-300 rounded flex items-center justify-center text-xs`}>📄</div>
}) => {
  const [svgContent, setSvgContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    const fetchSvg = async () => {
      try {
        setLoading(true);
        setError(false);

        console.log('SvgIcon: Processing src:', src);

        // If it's already a data URI, use it directly
        if (src.startsWith('data:image/svg+xml')) {
          console.log('SvgIcon: Using data URI directly');
          setSvgContent(src);
          setLoading(false);
          return;
        }

        // For S3 URLs or other external SVGs, fetch the content
        console.log('SvgIcon: Fetching external SVG from:', src);
        const response = await fetch(src, {
          method: 'GET',
          headers: {
            'Accept': 'image/svg+xml,text/plain,*/*',
          },
        });

        console.log('SvgIcon: Fetch response status:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const svgText = await response.text();
        console.log('SvgIcon: SVG text length:', svgText.length);
        console.log('SvgIcon: SVG text preview:', svgText.substring(0, 100));

        // Convert to data URI to avoid MIME type issues
        const dataUri = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgText)}`;
        setSvgContent(dataUri);

        console.log('SVG loaded successfully:', src);
      } catch (err) {
        console.error('Failed to load SVG:', src, err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    if (src) {
      fetchSvg();
    } else {
      console.log('SvgIcon: No src provided');
      setLoading(false);
      setError(true);
    }
  }, [src]);

  if (loading) {
    return (
      <div className={`${className} bg-gray-200 rounded animate-pulse flex items-center justify-center`}>
        <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
      </div>
    );
  }

  if (error || !svgContent) {
    return <>{fallback}</>;
  }

  return (
    <img
      src={svgContent}
      alt={alt}
      className={className}
      onError={() => {
        console.error('SVG display error:', src);
        setError(true);
      }}
    />
  );
};

export default SvgIcon;
