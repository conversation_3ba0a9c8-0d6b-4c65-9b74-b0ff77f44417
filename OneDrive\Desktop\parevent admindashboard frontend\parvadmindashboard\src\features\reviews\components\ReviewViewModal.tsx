import React from 'react';
import { X, <PERSON>, User, Calendar, Heart } from 'lucide-react';
import { Review, RELATIONSHIP_TYPES, EVENT_TYPES } from '../types/review';

interface ReviewViewModalProps {
  review: Review;
  isOpen: boolean;
  onClose: () => void;
}

const ReviewViewModal: React.FC<ReviewViewModalProps> = ({ review, isOpen, onClose }) => {
  if (!isOpen) return null;

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={20}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">Review Details</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Reviewer Header */}
          <div className="flex items-start gap-4 mb-6">
            {/* Reviewer Image */}
            <div className="flex-shrink-0">
              {review.imageUrl ? (
                <img
                  src={review.imageUrl}
                  alt={review.name}
                  className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                />
              ) : (
                <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
                  <User size={24} className="text-gray-500" />
                </div>
              )}
            </div>

            {/* Reviewer Info */}
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-xl font-semibold text-gray-900">{review.name}</h3>
                {review.isFeatured && (
                  <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-medium">
                    <Heart size={12} className="fill-current" />
                    Featured
                  </div>
                )}
              </div>

              {/* Rating */}
              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center gap-1">
                  {renderStars(review.star)}
                </div>
                <span className="text-lg font-semibold text-gray-700">
                  {review.star}/5
                </span>
              </div>

              {/* Meta Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <User size={14} className="text-gray-500" />
                  <span className="text-gray-600">Relationship:</span>
                  <span className="font-medium capitalize">
                    {RELATIONSHIP_TYPES.find(type => type.value === review.relationship)?.label || review.relationship}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar size={14} className="text-gray-500" />
                  <span className="text-gray-600">Event Type:</span>
                  <span className="font-medium capitalize">
                    {EVENT_TYPES.find(type => type.value === review.eventType)?.label || review.eventType}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Review Text */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Review</h4>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-gray-700 leading-relaxed">
                "{review.review}"
              </p>
            </div>
          </div>

          {/* Additional Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h5 className="font-semibold text-gray-900 mb-2">Review Status</h5>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Featured:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    review.isFeatured 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {review.isFeatured ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Rating:</span>
                  <span className="font-medium">{review.star} out of 5 stars</span>
                </div>
              </div>
            </div>

            <div>
              <h5 className="font-semibold text-gray-900 mb-2">Timestamps</h5>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span className="font-medium">
                    {new Date(review.createdAt).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Updated:</span>
                  <span className="font-medium">
                    {new Date(review.updatedAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReviewViewModal;
