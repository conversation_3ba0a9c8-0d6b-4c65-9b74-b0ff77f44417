import React from 'react';
import { Routes, Route } from 'react-router-dom';
import HeroSectionList from '../components/HeroSectionList';
import HeroSectionForm from '../components/HeroSectionForm';

const HeroSectionRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<HeroSectionList />} />
      <Route path="create" element={<HeroSectionForm mode="create" />} />
      <Route path="edit/:id" element={<HeroSectionForm mode="edit" />} />
    </Routes>
  );
};

export default HeroSectionRoutes;
