import { ColumnDef } from "@tanstack/react-table";
import { TeamType } from "../../type/teamType";
import { MoreHorizontal, Edit, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";


const imageUrl = "https://via.placeholder.com/40x40/6B7280/FFFFFF?text=👤"; // Default avatar image

interface ColumnActions {
  onEdit: (team: TeamType) => void;
  onDelete: (team: TeamType) => void;
}

interface TeamActionsProps {
  team: TeamType;
  onEdit: (team: TeamType) => void;
  onDelete: (team: TeamType) => void;
}

const TeamActions = ({ team, onEdit, onDelete }: TeamActionsProps) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onEdit(team)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onDelete(team)}>
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const SortOrderDisplay = ({ sortOrder }: { sortOrder: number }) => {
  return (
    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
      #{sortOrder}
    </span>
  );
};

export const createColumns = ({ onEdit, onDelete }: ColumnActions): ColumnDef<TeamType>[] => [
  {
    accessorKey: "id",
    header: ({ column }) => {
      return (
        <div
          className="flex flex-row cursor-pointer"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          S.No.
        </div>
      );
    },
    cell: ({ row }) => <div className="">{row.index + 1}</div>,
  },
  {
    accessorKey: "imageUrl",
    header: () => {
      return <div className="flex flex-row">Photo</div>;
    },
    cell: ({ row }) => {
      const team = row.original;
      const imageSrc = team.imageUrl || team.image || imageUrl;

      return (
        <div className="">
          <img
            src={imageSrc}
            className="w-10 h-10 object-cover rounded-full"
            alt="Team member photo"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = imageUrl;
            }}
          />
        </div>
      );
    },
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <div
          className="flex flex-row cursor-pointer"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
        </div>
      );
    },
    cell: ({ row }) => (
      <div className="font-medium">
        {row.getValue("name")}
      </div>
    ),
  },
  {
    accessorKey: "jobCategory",
    header: ({ column }) => {
      return (
        <div
          className="flex flex-row cursor-pointer"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Job Category
        </div>
      );
    },
    cell: ({ row }) => (
      <div className="max-w-xs">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {row.getValue("jobCategory")}
        </span>
      </div>
    ),
  },
  {
    accessorKey: "sortOrder",
    header: () => {
      return <div className="flex flex-row">Sort Order</div>;
    },
    cell: ({ row }) => (
      <div className="">
        <SortOrderDisplay sortOrder={row.getValue("sortOrder")} />
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => {
      return (
        <div
          className="flex flex-row cursor-pointer"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
        </div>
      );
    },
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            status === "active"
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {status}
        </span>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => {
      return (
        <div
          className="flex flex-row cursor-pointer"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Created At
        </div>
      );
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("createdAt"));
      return <div className="text-sm">{date.toLocaleDateString()}</div>;
    },
  },
  {
    accessorKey: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <TeamActions
        team={row.original}
        onEdit={onEdit}
        onDelete={onDelete}
      />
    ),
  },
];

// Legacy export for backward compatibility
export const columns = createColumns({
  onEdit: () => {},
  onDelete: () => {},
});
