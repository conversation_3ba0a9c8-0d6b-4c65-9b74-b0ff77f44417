import axios from 'axios';
import { heroSectionEndpoints } from '@/globalurl/baseurl';
import { CreateHeroSectionRequest, UpdateHeroSectionRequest, HeroSectionFilters } from '../types/heroSection';
import { mockHeroSections } from '@/utils/mockData';

export const heroSectionApi = {
  // Get all hero sections with optional filters
  getAll: async (filters?: HeroSectionFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.isPrimary !== undefined) params.append('isPrimary', filters.isPrimary.toString());
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);

      const url = params.toString() ? `${heroSectionEndpoints.getAll}?${params}` : heroSectionEndpoints.getAll;
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.warn('Hero Section API not available, using mock data:', error);
      return {
        success: true,
        data: mockHeroSections,
        message: 'Mock data loaded'
      };
    }
  },

  // Get primary hero section
  getPrimary: async () => {
    const response = await axios.get(heroSectionEndpoints.getPrimary);
    return response.data;
  },

  // Get hero section by ID or fixedId
  getById: async (id: string) => {
    const response = await axios.get(heroSectionEndpoints.getById(id));
    return response.data;
  },

  // Create new hero section
  create: async (data: CreateHeroSectionRequest) => {
    try {
      const formData = new FormData();

      formData.append('title', data.title);
      formData.append('subtitle', data.subtitle);
      formData.append('description', data.description);
      formData.append('buttonText', data.buttonText);
      formData.append('buttonLink', data.buttonLink);
      formData.append('isPrimary', data.isPrimary.toString());
      formData.append('displayDuration', data.displayDuration.toString());
      if (data.isActive !== undefined) formData.append('isActive', data.isActive.toString());
      if (data.sortOrder !== undefined) formData.append('sortOrder', data.sortOrder.toString());
      if (data.activeImageIndex !== undefined) formData.append('activeImageIndex', data.activeImageIndex.toString());
      formData.append('image', data.image);

      if (data.images && data.images.length > 0) {
        data.images.forEach(image => {
          formData.append('images', image);
        });
      }

      const response = await axios.post(heroSectionEndpoints.create, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Hero section create error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to create hero section',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Update hero section
  update: async (id: string, data: UpdateHeroSectionRequest) => {
    const formData = new FormData();
    if (data.title) formData.append('title', data.title);
    if (data.subtitle) formData.append('subtitle', data.subtitle);
    if (data.description) formData.append('description', data.description);
    if (data.buttonText) formData.append('buttonText', data.buttonText);
    if (data.buttonLink) formData.append('buttonLink', data.buttonLink);
    if (data.isPrimary !== undefined) formData.append('isPrimary', data.isPrimary.toString());
    if (data.displayDuration !== undefined) formData.append('displayDuration', data.displayDuration.toString());
    if (data.isActive !== undefined) formData.append('isActive', data.isActive.toString());
    if (data.sortOrder !== undefined) formData.append('sortOrder', data.sortOrder.toString());
    if (data.activeImageIndex !== undefined) formData.append('activeImageIndex', data.activeImageIndex.toString());
    if (data.image) formData.append('image', data.image);

    if (data.images && data.images.length > 0) {
      data.images.forEach(image => {
        formData.append('images', image);
      });
    }

    const response = await axios.put(heroSectionEndpoints.update(id), formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Delete hero section
  delete: async (id: string) => {
    const response = await axios.delete(heroSectionEndpoints.delete(id));
    return response.data;
  },
};
