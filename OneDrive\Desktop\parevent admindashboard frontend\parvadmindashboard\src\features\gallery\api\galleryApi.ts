import axios from 'axios';
import { galleryEndpoints } from '@/globalurl/baseurl';
import { CreateGalleryRequest, UpdateGalleryRequest, GalleryFilters } from '../types/gallery';
import { mockGalleries } from '@/utils/mockData';

export const galleryApi = {
  // Get all galleries with optional filters
  getAll: async (filters?: GalleryFilters) => {
    try {
      const params = new URLSearchParams();
      if (filters?.category) params.append('category', filters.category);
      if (filters?.keyword) params.append('keyword', filters.keyword);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder);

      const url = params.toString() ? `${galleryEndpoints.getAll}?${params}` : galleryEndpoints.getAll;
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.warn('Gallery API not available, using mock data:', error);
      // Return mock data when API is not available
      return {
        success: true,
        data: mockGalleries,
        message: 'Mock data loaded'
      };
    }
  },

  // Get gallery by ID or fixedId
  getById: async (id: string) => {
    try {
      const response = await axios.get(galleryEndpoints.getById(id));
      return response.data;
    } catch (error: any) {
      console.error('Gallery getById error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch gallery item',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Create new gallery
  create: async (data: CreateGalleryRequest) => {
    try {
      const formData = new FormData();

      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('keywords', JSON.stringify(data.keywords));
      formData.append('category', data.category);
      formData.append('sortOrder', data.sortOrder.toString());
      formData.append('image', data.image);

      const response = await axios.post(galleryEndpoints.create, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Gallery create error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to create gallery item',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Update gallery
  update: async (id: string, data: UpdateGalleryRequest) => {
    try {
      const formData = new FormData();
      if (data.title) formData.append('title', data.title);
      if (data.description) formData.append('description', data.description);
      if (data.keywords) formData.append('keywords', JSON.stringify(data.keywords));
      if (data.category) formData.append('category', data.category);
      if (data.sortOrder !== undefined) formData.append('sortOrder', data.sortOrder.toString());
      if (data.image) formData.append('image', data.image);

      const response = await axios.put(galleryEndpoints.update(id), formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Gallery update error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to update gallery item',
        error: error.response?.data?.debug || error.message
      };
    }
  },

  // Delete gallery
  delete: async (id: string) => {
    try {
      const response = await axios.delete(galleryEndpoints.delete(id));
      return response.data;
    } catch (error: any) {
      console.error('Gallery delete error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to delete gallery item',
        error: error.response?.data?.debug || error.message
      };
    }
  },
};
